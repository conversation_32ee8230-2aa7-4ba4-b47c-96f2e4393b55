"use client";

import { useMemo, useState } from "react";

interface UseNativeTableProps<TData> {
  data: TData[];
  columns: NativeTableColumn<TData>[];
}

/**
 * Normalise une chaîne de caractères pour la recherche en supprimant les accents et la casse
 */
const normalizeSearchString = (str: string): string => {
  return str
    .toLowerCase()
    .normalize("NFD")
    .replaceAll(/[\u0300-\u036F]/g, "")
    .trim();
};

/**
 * Extrait la valeur de recherche d'une colonne en gérant les types de données complexes
 */
const getColumnSearchValue = <TData>(
  item: TData,
  column: NativeTableColumn<TData>,
): string => {
  if (!column.accessorKey) return "";

  const value = (item as Record<string, unknown>)[column.accessorKey as string];

  if (value === null || value === undefined) return "";

  if (typeof value === "string") return value;

  if (typeof value === "object") {
    // Gestion spécifique pour les objets provider
    const obj = value as Record<string, unknown>;
    if (obj.firstName && obj.lastName) {
      return `${obj.firstName} ${obj.lastName} ${obj.extl_id || ""}`;
    }
    return JSON.stringify(value);
  }

  if (Array.isArray(value)) {
    // Gestion des tableaux (ex: categories)
    return value
      .map((item) => {
        if (
          typeof item === "object" &&
          item &&
          (item as Record<string, unknown>).name
        ) {
          return (item as Record<string, unknown>).name;
        }
        return String(item);
      })
      .join(" ");
  }

  return String(value);
};

/**
 * Helper functions for table data processing
 */
const applyColumnFilters = <TData>(
  data: TData[],
  columnFilters: Record<string, unknown>,
  columns: NativeTableColumn<TData>[],
): TData[] => {
  let filteredData = [...data];

  for (const [columnId, filterValue] of Object.entries(columnFilters)) {
    const column = columns.find((c) => c.id === columnId);
    if (column?.filterFn) {
      filteredData = filteredData.filter((item) => {
        // Création d'un objet row simple pour la fonction de filtre
        const row = {
          id: String(filteredData.indexOf(item)),
          original: item,
          index: filteredData.indexOf(item),
          getValue: (id: string) => {
            const col = columns.find((c) => c.id === id);
            return col?.accessorKey
              ? (item as Record<string, unknown>)[col.accessorKey as string]
              : undefined;
          },
        };
        return column.filterFn!(row, columnId, filterValue);
      });
    }
  }

  return filteredData;
};

const applyGlobalFilter = <TData>(
  data: TData[],
  globalFilter: string,
  searchColumn: string,
  columns: NativeTableColumn<TData>[],
): TData[] => {
  if (!globalFilter.trim()) return data;

  const normalizedSearchTerm = normalizeSearchString(globalFilter);

  return data.filter((item) => {
    if (searchColumn === "all") {
      // Recherche dans toutes les colonnes qui ont un accessorKey
      return columns.some((column) => {
        if (!column.accessorKey) return false;
        const columnValue = getColumnSearchValue(item, column);
        const normalizedValue = normalizeSearchString(columnValue);
        return normalizedValue.includes(normalizedSearchTerm);
      });
    } else {
      // Recherche dans la colonne spécifique sélectionnée
      const targetColumn = columns.find((col) => col.id === searchColumn);
      if (!targetColumn || !targetColumn.accessorKey) return false;

      const columnValue = getColumnSearchValue(item, targetColumn);
      const normalizedValue = normalizeSearchString(columnValue);
      return normalizedValue.includes(normalizedSearchTerm);
    }
  });
};

const getStatusSortValue = <TData>(item: TData): string => {
  return (item as Record<string, unknown>).isPublished ? "Publié" : "Brouillon";
};

const getProviderSortValue = <TData>(item: TData): string => {
  const provider = (item as Record<string, unknown>).provider as Record<
    string,
    unknown
  >;
  return `${provider?.firstName || ""} ${provider?.lastName || ""}`.trim();
};

const getCategoriesSortValue = <TData>(item: TData): string => {
  const categories =
    ((item as Record<string, unknown>).categories as Array<
      Record<string, unknown>
    >) || [];
  return categories.map((cat) => cat.name).join(", ");
};

const getDefaultSortValue = <TData>(
  item: TData,
  column: NativeTableColumn<TData>,
): string => {
  if (column.accessorKey) {
    return String(
      (item as Record<string, unknown>)[column.accessorKey as string] || "",
    );
  }
  return "";
};

const getSortValue = <TData>(
  item: TData,
  column: NativeTableColumn<TData>,
): string => {
  switch (column.id) {
    case "status":
      return getStatusSortValue(item);
    case "provider":
      return getProviderSortValue(item);
    case "categories":
      return getCategoriesSortValue(item);
    default:
      return getDefaultSortValue(item, column);
  }
};

const compareValues = (aValue: string, bValue: string): number => {
  // Gestion des valeurs nulles/undefined
  if (aValue == undefined && bValue == undefined) return 0;
  if (aValue == undefined) return 1;
  if (bValue == undefined) return -1;

  // Si ce sont des chaînes de caractères, utiliser localeCompare pour gérer les accents
  if (typeof aValue === "string" && typeof bValue === "string") {
    return aValue.localeCompare(bValue, "fr-FR", {
      sensitivity: "base", // Ignore la casse et les accents pour le tri
      numeric: true, // Tri numérique pour les nombres dans les chaînes
      ignorePunctuation: true, // Ignore la ponctuation
    });
  } else {
    // Tri par défaut pour les autres types
    if (aValue < bValue) return -1;
    else if (aValue > bValue) return 1;
    return 0;
  }
};

const applySorting = <TData>(
  data: TData[],
  sortConfig: { columnId: string; direction: "asc" | "desc" } | null,
  columns: NativeTableColumn<TData>[],
): TData[] => {
  if (!sortConfig) return data;

  const column = columns.find((c) => c.id === sortConfig.columnId);
  if (!column) return data;

  return [...data].sort((a, b) => {
    const aValue = getSortValue(a, column);
    const bValue = getSortValue(b, column);
    const result = compareValues(aValue, bValue);
    return sortConfig.direction === "desc" ? -result : result;
  });
};

/**
 * Hook principal pour la gestion d'un tableau natif React
 * Fournit les fonctionnalités de tri, filtrage et recherche
 */
export function useNativeTable<TData = unknown>({
  data,
  columns,
}: UseNativeTableProps<TData>) {
  // États de gestion du tableau
  const [sortConfig, setSortConfig] = useState<{
    columnId: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [columnFilters, setColumnFilters] = useState<Record<string, unknown>>(
    {},
  );
  const [globalFilter, setGlobalFilter] = useState<string>("");
  const [searchColumn, setSearchColumn] = useState<string>("description"); // Colonne de recherche sélectionnée

  // Création des instances de colonnes avec méthodes
  const columnInstances = useMemo(() => {
    return columns.map((column) => ({
      ...column,
      // Méthodes de tri
      getIsSorted: (): "asc" | "desc" | false => {
        if (!sortConfig || sortConfig.columnId !== column.id) return false;
        return sortConfig.direction;
      },
      toggleSorting: () => {
        if (column.enableSorting === false) return;

        setSortConfig((prev) => {
          // Si pas de tri ou colonne différente -> asc
          if (!prev || prev.columnId !== column.id) {
            return { columnId: column.id, direction: "asc" };
          }
          // Si asc -> desc
          if (prev.direction === "asc") {
            return { columnId: column.id, direction: "desc" };
          }
          // Si desc -> pas de tri
          return null;
        });
      },

      // Méthodes de filtrage
      getFilterValue: () => columnFilters[column.id] || "",
      setFilterValue: (value: unknown) => {
        if (column.enableFiltering === false) return;

        setColumnFilters((prev) => {
          if (!value || value === "") {
            const { [column.id]: _, ...rest } = prev;
            return rest;
          }
          return { ...prev, [column.id]: value };
        });
      },
    }));
  }, [columns, sortConfig, columnFilters]);

  // Traitement des données : filtrage + tri
  const processedRows = useMemo(() => {
    // 1. Application des filtres de colonnes
    let filteredData = applyColumnFilters(data, columnFilters, columns);

    // 2. Application du filtre global (recherche générique basée sur les colonnes)
    filteredData = applyGlobalFilter(
      filteredData,
      globalFilter,
      searchColumn,
      columns,
    );

    // 3. Application du tri avec gestion des colonnes complexes
    filteredData = applySorting(filteredData, sortConfig, columns);

    // 4. Conversion en format de lignes
    return filteredData.map((item, index) => ({
      id: String(index),
      original: item,
      index,
      getValue: (columnId: string) => {
        const column = columns.find((c) => c.id === columnId);
        return column?.accessorKey
          ? (item as Record<string, unknown>)[column.accessorKey as string]
          : undefined;
      },
    }));
  }, [data, columns, columnFilters, globalFilter, sortConfig]);

  // API simple du tableau
  return {
    // Données
    rows: processedRows,
    columns: columnInstances,

    // Filtrage global
    globalFilter,
    setGlobalFilter,
    searchColumn,
    setSearchColumn,

    // Utilitaires
    getColumn: (columnId: string) =>
      columnInstances.find((c) => c.id === columnId),
    resetFilters: () => {
      setColumnFilters({});
      setGlobalFilter("");
    },
    resetSorting: () => setSortConfig(null),
    reset: () => {
      setColumnFilters({});
      setGlobalFilter("");
      setSortConfig(null);
      setSearchColumn("description");
    },
  };
}
