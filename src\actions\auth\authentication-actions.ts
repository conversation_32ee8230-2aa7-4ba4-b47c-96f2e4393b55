"use server";

import { authService } from "@/services/auth";
import { CookieStorage } from "@/services/auth/token-storage";
import { FormLoginData } from "@/schemas";
import {
  createAuthError,
  handleNetworkError,
  logAuthError,
  getErrorDisplayMessage,
} from "@/lib/auth-error-handler";

/**
 * Authentication Actions
 * Focused on authentication operations: login, logout, token refresh
 * Following Single Responsibility Principle from clean code practices
 */

/**
 * Server action for user login
 * Handles authentication and cookie storage with proper error handling
 */
export async function authenticateUser(
  credentials: FormLoginData,
): Promise<LoginResponse> {
  try {
    // Call the auth service for pure API communication
    const authResponse = await authService.login(credentials);

    if (!authResponse) {
      const authError = createAuthError.invalidCredentials(
        "Invalid credentials or login failed",
      );
      logAuthError(authError, "authenticateUser");

      return {
        success: false,
        error: {
          code: authError.code,
          message: getErrorDisplayMessage(authError),
        },
      };
    }

    // Handle cookie storage on server side
    await CookieStorage.setTokens(authResponse);

    return {
      success: true,
      data: {
        admin: authResponse.admin,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
        message: "Login successful",
      },
    };
  } catch (error) {
    const authError = handleNetworkError(error);
    logAuthError(authError, "authenticateUser");

    return {
      success: false,
      error: {
        code: authError.code,
        message: getErrorDisplayMessage(authError),
        details: authError.details,
      },
    };
  }
}

/**
 * Server action for user logout
 * Handles cookie clearing on server side with proper error handling
 */
export async function logoutUser(): Promise<LogoutResponse> {
  try {
    // Clear cookies on server side
    await CookieStorage.clear();

    // Call the auth service for any API cleanup
    const result = await authService.logout();
    return result;
  } catch (error) {
    const authError = handleNetworkError(error);
    logAuthError(authError, "logoutUser");

    return {
      success: false,
      message: getErrorDisplayMessage(authError),
    };
  }
}

/**
 * Server action for token refresh
 * Handles token refresh and cookie updates with proper error handling
 */
export async function refreshUserToken(): Promise<LoginResponse> {
  try {
    // Get current refresh token from cookies
    const refreshToken = await CookieStorage.getRefreshToken();

    if (!refreshToken) {
      const authError = createAuthError.tokenExpired("No refresh token found");
      logAuthError(authError, "refreshUserToken");

      return {
        success: false,
        error: {
          code: authError.code,
          message: getErrorDisplayMessage(authError),
        },
      };
    }

    // Call auth service for token refresh
    const authResponse = await authService.refreshToken(refreshToken);

    if (!authResponse) {
      const authError = createAuthError.tokenInvalid("Token refresh failed");
      logAuthError(authError, "refreshUserToken");

      return {
        success: false,
        error: {
          code: authError.code,
          message: getErrorDisplayMessage(authError),
        },
      };
    }

    // Update cookies with new tokens
    await CookieStorage.setTokens(authResponse);

    return {
      success: true,
      data: {
        admin: authResponse.admin,
        accessToken: authResponse.accessToken,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt,
        message: "Token refreshed successfully",
      },
    };
  } catch (error) {
    const authError = handleNetworkError(error);
    logAuthError(authError, "refreshUserToken");

    return {
      success: false,
      error: {
        code: authError.code,
        message: getErrorDisplayMessage(authError),
        details: authError.details,
      },
    };
  }
}
