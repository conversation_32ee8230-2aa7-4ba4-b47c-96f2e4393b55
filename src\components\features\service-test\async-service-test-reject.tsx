import { getServiceDetailAction } from "@/actions";
import { getGlobalQueryParams } from "@/lib";
import { notFound } from "next/navigation";
import { ServiceTestReject } from "./service-test-reject";
import { SearchParams } from "nuqs";
import { GlobalQueryParamsType } from "@/types/global";

interface AsyncServiceTestRejectProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

/**
 * Async component for service test reject page
 * Fetches service data and renders the reject form
 * Follows the established async/sync component separation pattern
 */
export async function AsyncServiceTestReject({
  params,
  searchParams,
}: AsyncServiceTestRejectProps) {
  const { id } = await params;
  const query = await getGlobalQueryParams(searchParams);

  try {
    const response = await getServiceDetailAction(
      id,
      query as unknown as GlobalQueryParamsType,
    );

    if (!response.response || response.status === 404) {
      return notFound();
    }

    return <ServiceTestReject service={response.response} />;
  } catch (error) {
    throw error; // Let error boundary handle it
  }
}
