"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ChevronLeft, XCircle } from "lucide-react";
import { handleReject as rejectAction } from "@/actions/services";
import { toast } from "sonner";

interface ServiceTestRejectProps {
  service: Service;
}

/**
 * Service test reject page component
 * Provides a dedicated page for rejecting services with reason
 * Follows clean code principles with clear form validation
 */
export function ServiceTestReject({ service }: ServiceTestRejectProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();
  const [rejectionReason, setRejectionReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = () => {
    router.push(`/service-test/${service.id}`);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!rejectionReason.trim()) {
      toast.error(t("rejectionReasonRequired"));
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Service rejection started:", {
        serviceId: service.id,
        serviceName: service.name,
        providerId: service.provider.id,
        rejectionReason: rejectionReason,
        timestamp: new Date().toISOString(),
      });

      const result = await rejectAction({
        serviceId: service.id,
        reason: rejectionReason,
      });

      if (result.status >= 200 && result.status < 300) {
        toast.success(t("serviceRejectedSuccessfully"));
        console.log("Service rejected successfully:", {
          serviceId: service.id,
          serviceName: service.name,
          rejectionReason: rejectionReason,
          timestamp: new Date().toISOString(),
        });
        
        // Navigate back to service details
        router.push(`/service-test/${service.id}`);
      } else {
        throw new Error("Failed to reject service");
      }
    } catch (error) {
      console.error("Error rejecting service:", error);
      toast.error(error instanceof Error ? error.message : t("rejectionFailed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={handleBack}
          className="rounded-full"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {t("rejectService")}
          </h1>
          <p className="text-sm text-muted-foreground">
            {service.name}
          </p>
        </div>
      </div>

      {/* Reject Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <XCircle className="w-5 h-5" />
            {t("rejectionReason")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="rejectionReason">
                {t("rejectionReasonLabel")} *
              </Label>
              <Textarea
                id="rejectionReason"
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder={t("rejectionReasonPlaceholder")}
                rows={4}
                required
                className="min-h-[120px]"
              />
              <p className="text-xs text-muted-foreground">
                {t("rejectionReasonHint")}
              </p>
            </div>

            <div className="flex gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                disabled={isSubmitting}
                className="flex-1"
              >
                {t("cancel")}
              </Button>
              <Button
                type="submit"
                variant="destructive"
                disabled={isSubmitting || !rejectionReason.trim()}
                className="flex-1"
              >
                {isSubmitting ? t("rejecting") : t("rejectService")}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
