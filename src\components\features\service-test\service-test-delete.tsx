"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, Trash2, AlertTriangle } from "lucide-react";
import { handleDelete as deleteAction } from "@/actions/services";
import { toast } from "sonner";

interface ServiceTestDeleteProps {
  service: Service;
}

/**
 * Service test delete page component
 * Provides a dedicated page for deleting services with confirmation
 * Follows clean code principles with clear warning messages
 */
export function ServiceTestDelete({ service }: ServiceTestDeleteProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = () => {
    router.push(`/service-test/${service.id}`);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      console.log("Service deletion started:", {
        serviceId: service.id,
        serviceName: service.name,
        timestamp: new Date().toISOString(),
      });

      const result = await deleteAction({ id: service.id });

      if (result.status >= 200 && result.status < 300) {
        toast.success(t("serviceDeletedSuccessfully"));
        console.log("Service deleted successfully:", {
          serviceId: service.id,
          serviceName: service.name,
          timestamp: new Date().toISOString(),
        });
        
        // Navigate back to services list since service is deleted
        router.push("/services");
      } else {
        throw new Error("Failed to delete service");
      }
    } catch (error) {
      console.error("Error deleting service:", error);
      toast.error(error instanceof Error ? error.message : t("deletionFailed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={handleBack}
          className="rounded-full"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {t("deleteService")}
          </h1>
          <p className="text-sm text-muted-foreground">
            {service.name}
          </p>
        </div>
      </div>

      {/* Delete Confirmation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Trash2 className="w-5 h-5" />
            {t("confirmDeletion")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Warning Message */}
          <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <p className="text-sm font-medium text-red-800">
                {t("deleteWarningTitle")}
              </p>
              <p className="text-sm text-red-700">
                {t("deleteWarningMessage")}
              </p>
            </div>
          </div>

          {/* Service Summary */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("serviceName")}:</span>
              <span className="text-sm font-medium">{service.name}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("provider")}:</span>
              <span className="text-sm font-medium">
                {service.provider.first_name} {service.provider.last_name}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("currentStatus")}:</span>
              <Badge variant="secondary">
                {service.status}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("serviceId")}:</span>
              <span className="text-sm font-mono">{service.id}</span>
            </div>
          </div>

          {/* Confirmation Actions */}
          <div className="flex gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={isSubmitting}
              className="flex-1"
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSubmit}
              variant="destructive"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? t("deleting") : t("deleteService")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
