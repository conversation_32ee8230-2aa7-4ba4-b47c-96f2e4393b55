"use client";

import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { usePopup } from "@/providers/popup-provider";
import {
  handleApprove as approveAction,
  handleReject as rejectAction,
  handleDelete as deleteAction,
} from "@/actions/services";
import { ServiceBasicInfo } from "./service-basic-info";
import { ServiceDescriptions } from "./service-descriptions";
import { ServiceCategoriesAndLocations } from "./service-categories-and-locations";
import { ServiceStatusAndDates } from "./service-status-and-dates";
import { ServiceImages } from "./service-images";
import { PricingDetails } from "./pricing-details";
import { ServiceActionButtons } from "./service-action-buttons";

interface ServiceInformationProps {
  service: Service;
}

// Utility functions for popup creation
function createSuccessPopup(
  popup: PopupManagerActions,
  message: string,
  onConfirm: () => void,
) {
  return popup.create({
    title: "Success",
    description: message,
    size: "sm",
    actions: {
      cancel: {
        label: "Cancel",
        onClick: () => {},
      },
      confirm: {
        label: "OK",
        onClick: onConfirm,
      },
    },
  });
}

function createErrorPopup(popup: PopupManagerActions, error: unknown) {
  return popup.create({
    title: "Error",
    description: error instanceof Error ? error.message : "An error occurred",
    size: "sm",
    actions: {
      cancel: {
        label: "Cancel",
        onClick: () => {},
      },
      confirm: {
        label: "OK",
        onClick: () => {},
      },
    },
  });
}

function createInfoPopup(popup: PopupManagerActions, message: string) {
  return popup.create({
    title: "Information",
    description: message,
    size: "sm",
    actions: {
      cancel: {
        label: "Cancel",
        onClick: () => {},
      },
      confirm: {
        label: "OK",
        onClick: () => {},
      },
    },
  });
}

// Custom hook for service actions
function useServiceActions(service: Service) {
  const router = useRouter();
  const popup = usePopup();
  const t = useTranslations("ServiceDetails");

  // Loading states for each action
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleReject = () => {
    popup.create({
      title: t("rejectionReason"),
      size: "4xl",
      input: {
        placeholder: t("rejectionReasonLabel"),
        required: true,
        rows: 3,
        validation: (value: string) => value.trim().length > 0,
      },
      actions: {
        cancel: {
          label: t("cancel").toUpperCase(),
          onClick: () => {
            console.log("Service rejection cancelled:", {
              serviceId: service.id,
              serviceName: service.name,
              providerId: service.provider.id,
              timestamp: new Date().toISOString(),
            });
          },
        },
        confirm: {
          label: t("send").toUpperCase(),
          onClick: async (rejectionReason?: string) => {
            if (isRejecting || !rejectionReason?.trim()) return;

            setIsRejecting(true);

            try {
              console.log("Service rejection started:", {
                serviceId: service.id,
                serviceName: service.name,
                providerId: service.provider.id,
                rejectionReason: rejectionReason,
                timestamp: new Date().toISOString(),
              });

              const result = await rejectAction({
                serviceId: service.id,
                reason: rejectionReason,
              });

              // Check if the API response indicates success
              if (result.status >= 200 && result.status < 300) {
                createSuccessPopup(
                  popup,
                  "Service rejected successfully",
                  () => {
                    window.location.reload();
                  },
                );

                console.log("Service rejected successfully:", {
                  serviceId: service.id,
                  serviceName: service.name,
                  rejectionReason: rejectionReason,
                  timestamp: new Date().toISOString(),
                });
              } else {
                throw new Error("Failed to reject service");
              }
            } catch (error) {
              console.error("Error rejecting service:", error);
              createErrorPopup(popup, error);
            } finally {
              setIsRejecting(false);
            }
          },
        },
      },
      onOpen: () => {
        console.log("Rejection popup opened:", {
          serviceId: service.id,
          serviceName: service.name,
          timestamp: new Date().toISOString(),
        });
      },
    });
  };

  const handleDelete = () => {
    popup.create({
      title: "Delete Service",
      size: "lg",
      description:
        "Are you sure you want to delete this service? This action cannot be undone.",
      actions: {
        cancel: {
          label: t("cancel").toUpperCase(),
          onClick: () => {
            console.log("Service deletion cancelled:", {
              serviceId: service.id,
              serviceName: service.name,
              timestamp: new Date().toISOString(),
            });
          },
        },
        confirm: {
          label: t("delete").toUpperCase(),
          onClick: async () => {
            if (isDeleting) return;

            setIsDeleting(true);

            try {
              console.log("Service deletion started:", {
                serviceId: service.id,
                serviceName: service.name,
                timestamp: new Date().toISOString(),
              });

              const result = await deleteAction({ id: service.id });

              // Check if the API response indicates success
              if (result.status >= 200 && result.status < 300) {
                createSuccessPopup(
                  popup,
                  "Service deleted successfully",
                  () => {
                    router.push("/services");
                  },
                );
              } else {
                throw new Error("Failed to delete service");
              }
            } catch (error) {
              console.error("Error deleting service:", error);
              createErrorPopup(popup, error);
            } finally {
              setIsDeleting(false);
            }
          },
        },
      },
    });
  };

  const handleValidate = async () => {
    if (isApproving) return; // Prevent double clicks

    // Check if service is already approved
    if (service.status === "APPROVED" || service.status === "ACTIVE") {
      createInfoPopup(popup, "This service is already approved.");
      return;
    }

    setIsApproving(true);

    try {
      console.log("Service approval started:", {
        serviceId: service.id,
        serviceName: service.name,
        timestamp: new Date().toISOString(),
      });

      const result = await approveAction({ serviceId: service.id });

      // Check if the API response indicates success
      if (result.status >= 200 && result.status < 300) {
        createSuccessPopup(popup, "Service approved successfully", () => {
          window.location.reload();
        });
      } else {
        throw new Error("Failed to approve service");
      }
    } catch (error) {
      console.error("Error approving service:", error);
      createErrorPopup(popup, error);
    } finally {
      setIsApproving(false);
    }
  };

  return {
    handleReject,
    handleDelete,
    handleValidate,
    isApproving,
    isRejecting,
    isDeleting,
  };
}

// Separate component for action buttons to reduce complexity

export function ServiceInformation({ service }: ServiceInformationProps) {
  const t = useTranslations("ServiceDetails");
  const {
    handleReject,
    handleDelete,
    handleValidate,
    isApproving,
    isRejecting,
    isDeleting,
  } = useServiceActions(service);

  return (
    <div className="w-full bg-background border">
      {/* Header */}
      <div className="flex flex-col p-0 border-b border-border rounded-t-[10px]">
        <ServiceStatusAndDates service={service} />
      </div>

      {/* Content */}
      <div className="flex flex-col gap-6 px-5 py-2.5">
        <ServiceBasicInfo service={service} />
        <ServiceDescriptions service={service} />
        <ServiceCategoriesAndLocations service={service} />
        <ServiceImages images={service.img} serviceName={service.name} />
        <PricingDetails pricing={service.pricing} />

        {/* About Provider Section */}
        <div className="flex flex-col w-full">
          <label className="text-xs text-foreground font-['Poppins'] h-[35px] flex items-center">
            {t("aboutProviderLabel")}
          </label>
          <div className="bg-background border border-border rounded-[10px] px-2.5 py-1.5 w-full min-h-[80px]">
            <p className="text-xs text-foreground font-['Poppins'] leading-relaxed">
              {service.provider.unsafe_metadata?.about_me || t("notAvailable")}
            </p>
          </div>
        </div>

        <ServiceActionButtons
          service={service}
          onDelete={handleDelete}
          onReject={handleReject}
          onApprove={handleValidate}
          isDeleting={isDeleting}
          isRejecting={isRejecting}
          isApproving={isApproving}
        />
      </div>
    </div>
  );
}
