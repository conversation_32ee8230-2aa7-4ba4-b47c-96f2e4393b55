"use client";

import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Trash2, XCircle, CheckCircle } from "lucide-react";

interface ServiceTestActionButtonsProps {
  service: Service;
}

/**
 * Action buttons component for service test
 * Navigates to dedicated pages instead of opening popups
 * Follows clean code principles with clear separation of concerns
 */
export function ServiceTestActionButtons({ service }: ServiceTestActionButtonsProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();

  const handleDelete = () => {
    router.push(`/service-test/${service.id}/delete`);
  };

  const handleReject = () => {
    router.push(`/service-test/${service.id}/reject`);
  };

  const handleApprove = () => {
    router.push(`/service-test/${service.id}/approve`);
  };

  const isAlreadyApproved = service.status === "APPROVED" || service.status === "ACTIVE";
  const isAlreadyRejected = service.status === "REJECTED";

  return (
    <div className="flex justify-center items-center gap-6">
      {/* Delete Button */}
      <Button
        variant="destructive"
        size="lg"
        onClick={handleDelete}
        className="flex items-center gap-2 min-w-[120px]"
      >
        <Trash2 className="w-4 h-4" />
        {t("delete")}
      </Button>

      {/* Reject Button */}
      {!isAlreadyRejected && (
        <Button
          variant="outline"
          size="lg"
          onClick={handleReject}
          className="flex items-center gap-2 min-w-[120px] border-orange-500 text-orange-600 hover:bg-orange-50"
        >
          <XCircle className="w-4 h-4" />
          {t("reject")}
        </Button>
      )}

      {/* Approve Button */}
      {!isAlreadyApproved && (
        <Button
          variant="default"
          size="lg"
          onClick={handleApprove}
          className="flex items-center gap-2 min-w-[120px] bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="w-4 h-4" />
          {t("approve")}
        </Button>
      )}

      {/* Status indicators for already processed services */}
      {isAlreadyApproved && (
        <div className="flex items-center gap-2 text-green-600 font-medium">
          <CheckCircle className="w-4 h-4" />
          {t("alreadyApproved")}
        </div>
      )}

      {isAlreadyRejected && (
        <div className="flex items-center gap-2 text-red-600 font-medium">
          <XCircle className="w-4 h-4" />
          {t("alreadyRejected")}
        </div>
      )}
    </div>
  );
}
