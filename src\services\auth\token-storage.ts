import "server-only";
import { DEFAULT_AUTH_CONFIG } from "@/config/env";
import { cookies } from "next/headers";

/**
 * Clean Token Storage Strategy
 * Uses cookies for server-side persistence
 * Simple and focused on single responsibility
 */
export class CookieStorage {
  private static readonly keys = DEFAULT_AUTH_CONFIG.keys;
  private static readonly COOKIE_OPTIONS = {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax" as const,
    maxAge: 60 * 60 * 24 * 30, // 30 days
    path: "/",
  };

  /**
   * Store authentication tokens and user data
   */
  static async setTokens(authResponse: AuthResponse): Promise<void> {
    try {
      const cookieStore = await cookies();
      cookieStore.set(
        this.keys.accessToken,
        authResponse.accessToken,
        this.COOKIE_OPTIONS,
      );
      cookieStore.set(
        this.keys.refreshToken,
        authResponse.refreshToken,
        this.COOKIE_OPTIONS,
      );
      cookieStore.set(
        this.keys.user,
        JSON.stringify(authResponse.admin),
        this.COOKIE_OPTIONS,
      );
      cookieStore.set(
        this.keys.expiresAt,
        authResponse.expiresAt,
        this.COOKIE_OPTIONS,
      );
    } catch (error) {
      console.error("Failed to store tokens:", error);
    }
  }

  /**
   * Get access token
   */
  static async getAccessToken(): Promise<string | null> {
    try {
      const cookieStore = await cookies();
      return cookieStore.get(this.keys.accessToken)?.value || null;
    } catch (error) {
      console.error("Failed to get access token:", error);
      return null;
    }
  }

  /**
   * Get refresh token
   */
  static async getRefreshToken(): Promise<string | null> {
    try {
      const cookieStore = await cookies();
      return cookieStore.get(this.keys.refreshToken)?.value || null;
    } catch (error) {
      console.error("Failed to get refresh token:", error);
      return null;
    }
  }

  /**
   * Get stored user data
   */
  static async getUser(): Promise<Admin | null> {
    try {
      const cookieStore = await cookies();
      const userData = cookieStore.get(this.keys.user)?.value;
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Failed to get user data:", error);
      return null;
    }
  }

  /**
   * Get token expiration time
   */
  static async getExpiresAt(): Promise<string | null> {
    try {
      const cookieStore = await cookies();
      return cookieStore.get(this.keys.expiresAt)?.value || null;
    } catch (error) {
      console.error("Failed to get expiration time:", error);
      return null;
    }
  }

  /**
   * Check if tokens are expired
   */
  static async isExpired(): Promise<boolean> {
    const expiresAt = await this.getExpiresAt();
    if (!expiresAt) return true;

    try {
      const expirationTime = new Date(expiresAt).getTime();
      const currentTime = Date.now();
      const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

      return currentTime >= expirationTime - bufferTime;
    } catch {
      return true;
    }
  }

  /**
   * Clear all stored tokens and user data
   */
  static async clear(): Promise<void> {
    try {
      const cookieStore = await cookies();
      cookieStore.delete(this.keys.accessToken);
      cookieStore.delete(this.keys.refreshToken);
      cookieStore.delete(this.keys.user);
      cookieStore.delete(this.keys.expiresAt);
    } catch (error) {
      console.error("Failed to clear tokens:", error);
    }
  }

  /**
   * Check if user is authenticated (has valid tokens)
   */
  static async isAuthenticated(): Promise<boolean> {
    const [accessToken, user, isExpired] = await Promise.all([
      this.getAccessToken(),
      this.getUser(),
      this.isExpired(),
    ]);

    return !!(accessToken && user && !isExpired);
  }

  /**
   * Get all authentication data
   */
  static async getAuthData(): Promise<{
    accessToken: string | null;
    refreshToken: string | null;
    user: Admin | null;
    expiresAt: string | null;
    isAuthenticated: boolean;
  }> {
    const [accessToken, refreshToken, user, expiresAt, isAuthenticated] =
      await Promise.all([
        this.getAccessToken(),
        this.getRefreshToken(),
        this.getUser(),
        this.getExpiresAt(),
        this.isAuthenticated(),
      ]);

    return {
      accessToken,
      refreshToken,
      user,
      expiresAt,
      isAuthenticated,
    };
  }

  /**
   * Update user data only (after profile updates)
   */
  static async updateUser(user: Admin): Promise<void> {
    try {
      const cookieStore = await cookies();
      cookieStore.set(
        this.keys.user,
        JSON.stringify(user),
        this.COOKIE_OPTIONS,
      );
    } catch (error) {
      console.error("Failed to update user data:", error);
    }
  }
}
