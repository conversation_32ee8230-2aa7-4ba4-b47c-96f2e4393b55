"use client";

import { ServiceTestContent } from "./service-test-content";
import { useRouter } from "next/navigation";

interface ServiceTestProps {
  service: Service;
}

/**
 * Sync component for service test presentation
 * Follows the established async/sync component separation pattern
 * Provides improved design and navigation compared to service details
 */
export function ServiceTest({ service }: ServiceTestProps) {
  const router = useRouter();

  const handleBack = () => {
    router.push("/services", { scroll: false });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <ServiceTestContent service={service} onBack={handleBack} />
    </div>
  );
}
