import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";

interface ProviderHeaderProps {
  provider: Provider;
}

export function ProviderHeader({ provider }: ProviderHeaderProps) {
  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || "U";
    const last = lastName?.charAt(0) || "N";
    return `${first}${last}`.toUpperCase();
  };

  const fullName =
    [provider.firstName, provider.lastName].filter(Boolean).join(" ") ||
    "Unknown User";

  return (
    <div className="flex flex-col justify-center items-center gap-2.5 flex-1 h-full rounded-[10px]">
      <Avatar className="w-[150px] h-[150px] border border-border">
        <AvatarImage src={provider.imageUrl || undefined} alt={fullName} />
        <AvatarFallback className="text-4xl font-['Poppins'] text-foreground">
          {getInitials(provider.firstName, provider.lastName)}
        </AvatarFallback>
      </Avatar>
    </div>
  );
}
