import { ProviderHeader } from "./provider-header";
import { ProviderDetails } from "./provider-details";

interface ProviderInformationProps {
  provider: Provider;
  name: string;
}

export function ProviderInformation({
  provider,
  name,
}: ProviderInformationProps) {
  return (
    <div className="w-full bg-white rounded-t-[10px] rounded-b-[0px] border-0 p-2.5 flex flex-row justify-stretch items-stretch">
      <ProviderHeader provider={provider} />
      <ProviderDetails provider={provider} name={name} />
    </div>
  );
}
