import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";

interface ServiceTestCategoriesAndLocationsProps {
  service: Service;
}

/**
 * Categories and locations component for service test
 * Displays service categories and locations with improved styling using badges
 * Follows clean code principles with better visual presentation
 */
export function ServiceTestCategoriesAndLocations({
  service,
}: ServiceTestCategoriesAndLocationsProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <div className="space-y-4">
      {/* Categories */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          {t("categories")}
        </h3>
        <div className="bg-muted/50 border border-border rounded-lg px-4 py-3">
          {service.categories && service.categories.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {service.categories.map((category, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {category}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-sm text-muted-foreground">
              {t("noCategories")}
            </span>
          )}
        </div>
      </div>

      {/* Locations */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          {t("locations")}
        </h3>
        <div className="bg-muted/50 border border-border rounded-lg px-4 py-3">
          {service.locations && service.locations.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {service.locations.map((location, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {location}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-sm text-muted-foreground">
              {t("noLocations")}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
