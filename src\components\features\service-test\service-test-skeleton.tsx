import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft } from "lucide-react";

/**
 * Loading skeleton for service test page
 * Provides visual feedback while data is being fetched
 * Matches the layout structure of the actual content
 */
export function ServiceTestSkeleton() {
  return (
    <div className="flex flex-col items-center gap-2.5 px-5 pb-2.5">
      {/* Navigation Header */}
      <div className="flex justify-between items-center w-full gap-14 px-2.5 py-2">
        <div className="flex items-center gap-2.5">
          <div className="flex justify-center items-center w-9 h-9 bg-background border border-border rounded-full">
            <ChevronLeft className="w-4 h-4 text-foreground" />
          </div>
          <Skeleton className="h-4 w-12" />
        </div>
        <div className="w-6 h-6" />
      </div>

      {/* Provider Information Skeleton */}
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-4">
            <Skeleton className="w-16 h-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Service Information Skeleton */}
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="p-6">
          {/* Two Column Layout Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Descriptions and Details */}
            <div className="space-y-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-12 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-20 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-16 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-24 w-full" />
              </div>
            </div>

            {/* Right Column - Images */}
            <div className="space-y-4">
              <Skeleton className="h-4 w-32" />
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Skeleton className="aspect-square w-full rounded-lg" />
                <Skeleton className="aspect-square w-full rounded-lg" />
                <Skeleton className="aspect-square w-full rounded-lg" />
                <Skeleton className="aspect-square w-full rounded-lg" />
              </div>
            </div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex justify-center items-center gap-6 pt-8 mt-8 border-t border-border">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
