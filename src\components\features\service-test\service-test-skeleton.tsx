import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft } from "lucide-react";

/**
 * Loading skeleton for service test page
 * Provides visual feedback while data is being fetched
 * Matches the layout structure of the actual content
 */
export function ServiceTestSkeleton() {
  return (
    <div className="flex flex-col items-center gap-2.5 px-5 pb-2.5">
      {/* Navigation Header */}
      <div className="flex justify-between items-center w-full gap-14 px-2.5 py-2">
        <div className="flex items-center gap-2.5">
          <div className="flex justify-center items-center w-9 h-9 bg-background border border-border rounded-full">
            <ChevronLeft className="w-4 h-4 text-foreground" />
          </div>
          <Skeleton className="h-4 w-12" />
        </div>
        <div className="w-6 h-6" />
      </div>

      {/* Provider Information Skeleton */}
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center gap-4">
            <Skeleton className="w-16 h-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Service Information Skeleton */}
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          
          {/* Images skeleton */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Skeleton className="h-32 w-full rounded-lg" />
            <Skeleton className="h-32 w-full rounded-lg" />
            <Skeleton className="h-32 w-full rounded-lg" />
          </div>

          {/* Action buttons skeleton */}
          <div className="flex justify-center items-center gap-12 pt-4">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
