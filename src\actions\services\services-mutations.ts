// Services mutations and server actions
// This file contains server actions for services CRUD operations

"use server";

import { ServicesServices } from "@/services/services/services-services";
import {
  RejectServiceParamsSchema,
  ValidateServiceParamsSchema,
  DeleteServiceParamsSchema,
} from "@/schemas/services/service-schema";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { ServiceAnalytics } from "@/utils/service-analytics";

// Schema for approve action
const ApproveServiceParamsSchema = z.object({
  serviceId: z.number(),
});

/**
 * Server action for approving a service
 * @param params - Approval parameters containing serviceId
 * @returns Promise<ApiResponse<Service>>
 */
export async function handleApprove(params: {
  serviceId: number;
}): Promise<ApiResponse<Service>> {
  let validatedParams: { serviceId: number } | undefined;

  try {
    // Validate input parameters
    validatedParams = ApproveServiceParamsSchema.parse(params);

    console.log(
      `[handleApprove] Starting approval for service ${validatedParams.serviceId}`,
    );

    // Call the service method
    const servicesService = new ServicesServices();
    const result = await servicesService.approve(validatedParams.serviceId);

    console.log(
      `[handleApprove] Service ${validatedParams.serviceId} approved successfully`,
    );

    // Track analytics event for service approval
    ServiceAnalytics.trackAction({
      action: "service_approved",
      serviceId: validatedParams.serviceId,
      timestamp: new Date().toISOString(),
      success: true,
    });

    // Revalidate relevant paths to update cached data
    revalidatePath("/services");
    revalidatePath(`/services/${validatedParams.serviceId}`);

    return result;
  } catch (error) {
    console.error("[handleApprove] Error approving service:", error);

    // Track failed approval attempt (if we have validated params)
    if (validatedParams) {
      ServiceAnalytics.trackAction({
        action: "service_approved",
        serviceId: validatedParams.serviceId,
        timestamp: new Date().toISOString(),
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }

    if (error instanceof z.ZodError) {
      throw new Error(
        `Validation error: ${error.errors.map((e) => e.message).join(", ")}`,
      );
    }

    if (error instanceof Error) {
      throw error;
    }

    throw new Error("Unknown error occurred while approving service");
  }
}

/**
 * Server action for rejecting a service with a rejection reason
 * This action atomically updates both isPublished and rejectionReason fields
 * @param params - Rejection parameters containing serviceId and reason
 * @returns Promise<ApiResponse<Service>>
 */
export async function handleReject(
  params: RejectServiceParams,
): Promise<ApiResponse<Service>> {
  try {
    // Validate input parameters
    const validatedParams = RejectServiceParamsSchema.parse(params);

    console.log(
      `[handleReject] Starting rejection for service ${validatedParams.serviceId} with reason: ${validatedParams.reason}`,
    );

    // Call the service method
    const servicesService = new ServicesServices();
    const result = await servicesService.reject(
      validatedParams.serviceId,
      validatedParams.reason,
    );

    console.log(
      `[handleReject] Service ${validatedParams.serviceId} rejected successfully`,
    );

    // Track analytics event for service rejection
    ServiceAnalytics.trackAction({
      action: "service_rejected",
      serviceId: validatedParams.serviceId,
      reason: validatedParams.reason,
      timestamp: new Date().toISOString(),
      success: true,
    });

    // Revalidate relevant paths to update cached data
    revalidatePath("/services");
    revalidatePath(`/services/${validatedParams.serviceId}`);

    return result;
  } catch (error) {
    console.error("[handleReject] Error rejecting service:", error);

    if (error instanceof z.ZodError) {
      throw new Error(
        `Validation error: ${error.errors.map((e) => e.message).join(", ")}`,
      );
    }

    if (error instanceof Error) {
      throw error;
    }

    throw new Error("Unknown error occurred while rejecting service");
  }
}

/**
 * Server action for deleting a service
 * @param params - Delete parameters containing service id
 * @returns Promise<ApiResponse<void>>
 */
export async function handleDelete(
  params: DeleteServiceParams,
): Promise<ApiResponse<void>> {
  try {
    // Validate input parameters
    const validatedParams = DeleteServiceParamsSchema.parse(params);

    console.log(
      `[handleDelete] Starting deletion for service ${validatedParams.id}`,
    );

    // Call the service method
    const servicesService = new ServicesServices();
    const result = await servicesService.delete(validatedParams);

    console.log(
      `[handleDelete] Service ${validatedParams.id} deleted successfully`,
    );

    // Track analytics event for service deletion
    ServiceAnalytics.trackAction({
      action: "service_deleted",
      serviceId: validatedParams.id,
      timestamp: new Date().toISOString(),
      success: true,
    });

    // Revalidate relevant paths to update cached data
    revalidatePath("/services");
    revalidatePath(`/services/${validatedParams.id}`);

    return result;
  } catch (error) {
    console.error("[handleDelete] Error deleting service:", error);

    if (error instanceof z.ZodError) {
      throw new Error(
        `Validation error: ${error.errors.map((e) => e.message).join(", ")}`,
      );
    }

    if (error instanceof Error) {
      throw error;
    }

    throw new Error("Unknown error occurred while deleting service");
  }
}

/**
 * Server action for validating/publishing a service
 * @param params - Validation parameters containing serviceId and isPublished status
 * @returns Promise<ApiResponse<Service>>
 */
export async function handleValidate(
  params: ValidateServiceParams,
): Promise<ApiResponse<Service>> {
  try {
    // Validate input parameters
    const validatedParams = ValidateServiceParamsSchema.parse(params);

    const action = validatedParams.isPublished ? "publishing" : "unpublishing";
    console.log(
      `[handleValidate] Starting ${action} for service ${validatedParams.serviceId}`,
    );

    // Call the service method
    const servicesService = new ServicesServices();
    const result = await servicesService.validate(validatedParams);

    console.log(
      `[handleValidate] Service ${validatedParams.serviceId} ${action} completed successfully`,
    );

    // Revalidate relevant paths to update cached data
    revalidatePath("/services");
    revalidatePath(`/services/${validatedParams.serviceId}`);

    return result;
  } catch (error) {
    console.error("[handleValidate] Error validating service:", error);

    if (error instanceof z.ZodError) {
      throw new Error(
        `Validation error: ${error.errors.map((e) => e.message).join(", ")}`,
      );
    }

    if (error instanceof Error) {
      throw error;
    }

    throw new Error("Unknown error occurred while validating service");
  }
}
