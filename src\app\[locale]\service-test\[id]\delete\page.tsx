import {
  AsyncServiceTestDelete,
  ServiceTestDeleteSkeleton,
} from "@/components/features/service-test";
import { SearchParams } from "nuqs";
import { Suspense } from "react";

interface ServiceTestDeletePageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

export default function ServiceTestDeletePage({
  params,
  searchParams,
}: ServiceTestDeletePageProps) {
  return (
    <div className="flex flex-col gap-2.5 py-2.5 px-5">
      <Suspense fallback={<ServiceTestDeleteSkeleton />}>
        <AsyncServiceTestDelete params={params} searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
