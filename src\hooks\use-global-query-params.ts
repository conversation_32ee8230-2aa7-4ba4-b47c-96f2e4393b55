import { parseAsInteger, parseAsString, useQueryStates } from "nuqs";
// import { useMemo } from "react";

export function useGlobalQueryParams() {
  const [searchParams, setSearchParams] = useQueryStates(
    {
      // Pagination
      limit: parseAsInteger.withDefault(10),
      next: parseAsInteger.withDefault(1),

      // Search and filtering
      search: parseAsString,
      status: parseAsString,
      category: parseAsString,
      providerId: parseAsInteger,

      // Sorting
      sortBy: parseAsString,
      sortOrder: parseAsString,

      // Legacy parameters (keep for backward compatibility)
      extl_id: parseAsString,
      currency: parseAsString.withDefault("EUR"),
      display: parseAsString,
    },
    {
      history: "push",
      shallow: false,
      clearOnDefault: true, // Remove parameters from URL when they are undefined/null
    },
  );

  // const queryLength = useMemo(() => {
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  //   return count = 0;
  // }, []);

  return {
    searchParams,
    setSearchParams,
    // queryLength,
  };
}
