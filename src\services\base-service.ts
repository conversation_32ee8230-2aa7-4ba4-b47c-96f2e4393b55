import { GlobalQueryParamsType } from "@/types/global";
import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { z } from "zod";

export type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

export interface ApiRequestOptions {
  method?: HttpMethod;
  body?: Record<string, string | number | boolean | string[]> | FormData;
  headers?: Record<string, string>;
  authenticated?: boolean;
  query?: Partial<GlobalQueryParamsType>;
  tag?: string;
}

export class ApiError extends Error {
  status: number;
  details?: unknown;

  constructor(message: string, status: number, details?: unknown) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.details = details;
  }
}

export abstract class BaseApiService {
  protected defaultHeaders: Record<string, string>;
  protected tokenCookieName: string;

  constructor(
    tokenCookieName: string = "access_token",
    defaultHeaders: Record<string, string> = {},
  ) {
    this.tokenCookieName = tokenCookieName;
    this.defaultHeaders = {
      "Content-Type": "application/json",
      ...defaultHeaders,
    };
  }

  /**
   * Remove the access token cookie
   */
  public async clearAccessToken(): Promise<void> {
    const cookieStore = await cookies();
    cookieStore.delete(this.tokenCookieName);
  }

  /**
   * Get the access token from cookies
   * @returns The access token or null if not found
   */
  protected async getAuthToken(): Promise<string | null> {
    const cookieStore = await cookies();
    return cookieStore.get("access_token")?.value ?? null;
  }

  /**
   * Construct full URL with query parameters
   */
  private async buildUrl(
    endpoint: string,
    query?: Partial<GlobalQueryParamsType>,
  ): Promise<string> {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_API_URL?.startsWith("/")
      ? process.env.NEXT_PUBLIC_BASE_API_URL
      : `${process.env.NEXT_PUBLIC_BASE_API_URL}/`;
    const url = new URL(`${baseUrl}${endpoint.replace(/^\//, "")}`);

    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        if (value == null) return;

        if (Array.isArray(value)) {
          if (value.length === 0) return;
          value.forEach((v) => {
            if (v != null) {
              url.searchParams.append(key, v.toString());
            }
          });
        } else {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    return url.toString();
  }

  /**
   * Prepare headers for the request
   */
  private async prepareHeaders(options: ApiRequestOptions): Promise<Headers> {
    const headers = new Headers(this.defaultHeaders);

    // Add custom headers
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        headers.set(key, value);
      });
    }

    // Handle authentication
    if (options.authenticated) {
      const token = await this.getAuthToken();
      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }
    }

    return headers;
  }

  /**
   * Process and validate the API response
   */
  private async processResponse<T>(
    response: Response,
    responseSchema?: z.ZodType<T>,
  ): Promise<ApiResponse<T>> {
    console.log("Processing response:", response);

    if (!response.ok) {
      if (response.status === 401) {
        return redirect("/fr/logout");
      }
      throw new ApiError(
        `HTTP error! status: ${response.status}`,
        response.status,
      );
    }

    const responseData = await response.json();
    if (responseSchema) {
      const validationResult = responseSchema.safeParse(responseData);
      if (!validationResult.success) {
        throw new ApiError(
          "Response Validation Failed",
          422,
          validationResult.error,
        );
      }
    }

    return {
      status: response.status,
      response: responseData,
    };
  }

  /**
   * Make an API request with comprehensive error handling
   */
  protected async request<T>(
    endpoint: string,
    options: ApiRequestOptions = {},
    responseSchema?: z.ZodType<T>,
  ): Promise<ApiResponse<T>> {
    const { method = "GET", body, query } = options;
    const url = await this.buildUrl(endpoint, query);
    const headers = await this.prepareHeaders(options);

    const fetchOptions: RequestInit = {
      method,
      headers,
      ...(body && {
        body: body instanceof FormData ? body : JSON.stringify(body),
      }),
      next: {
        tags: [options.tag ?? ""],
      },
    };

    const response = await fetch(url, fetchOptions);
    return this.processResponse(response, responseSchema);
  }
}
