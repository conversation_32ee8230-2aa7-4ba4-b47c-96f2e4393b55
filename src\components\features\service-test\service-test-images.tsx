import { useTranslations } from "next-intl";
import Image from "next/image";

interface ServiceTestImagesProps {
  images: string[];
  serviceName: string;
}

/**
 * Service images component for service test
 * Displays service images in an improved grid layout
 * Follows clean code principles with responsive design
 */
export function ServiceTestImages({
  images,
  serviceName,
}: ServiceTestImagesProps) {
  const t = useTranslations("ServiceDetails");

  if (!images || images.length === 0) {
    return (
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          {t("serviceImages")}
        </h3>
        <div className="bg-muted/50 border border-border rounded-lg px-4 py-8 text-center">
          <span className="text-sm text-muted-foreground">{t("noImages")}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-foreground">
        {t("serviceImages")} ({images.length})
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {images.map((imageUrl, index) => (
          <div
            key={index}
            className="relative aspect-square bg-muted border border-border rounded-lg overflow-hidden group hover:shadow-md transition-shadow"
          >
            <Image
              src={imageUrl}
              alt={`${serviceName} - Image ${index + 1}`}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
            />
          </div>
        ))}
      </div>
    </div>
  );
}
