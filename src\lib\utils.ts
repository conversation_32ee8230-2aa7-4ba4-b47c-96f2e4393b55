import { parseAsInteger, parseAsString, createLoader } from "nuqs/server";

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const globalQueryParams = {
  // Pagination
  limit: parseAsInteger.withDefault(10),
  next: parseAsInteger.withDefault(1), // Maps to 'page' in API

  // Search and filtering
  search: parseAsString,
  status: parseAsString,
  category: parseAsString,
  providerId: parseAsInteger,

  // Sorting
  sortBy: parseAsString,
  sortOrder: parseAsString,

  // Legacy parameters (keep for backward compatibility)
  extl_id: parseAsString,
  currency: parseAsString.withDefault("EUR"),
  display: parseAsString,
};

export const getGlobalQueryParams = createLoader(globalQueryParams);
