/**
 * Service Analytics Utility
 * Tracks service management actions for monitoring and analytics
 */

export interface ServiceActionEvent {
  action: "service_approved" | "service_rejected" | "service_deleted";
  serviceId: number;
  serviceName?: string;
  reason?: string;
  timestamp: string;
  success: boolean;
  duration?: number;
  error?: string;
}

export class ServiceAnalytics {
  private static events: ServiceActionEvent[] = [];

  /**
   * Track a service management action
   */
  static trackAction(event: ServiceActionEvent): void {
    this.events.push(event);

    // Log to console for development
    console.info(`[SERVICE_ANALYTICS] ${event.action}:`, event);

    // In production, you would send this to your analytics service
    // Example: analytics.track(event.action, event);
  }

  /**
   * Get analytics summary for the current session
   */
  static getSummary(): {
    totalActions: number;
    approvals: number;
    rejections: number;
    deletions: number;
    successRate: number;
  } {
    const total = this.events.length;
    const approvals = this.events.filter(
      (e) => e.action === "service_approved",
    ).length;
    const rejections = this.events.filter(
      (e) => e.action === "service_rejected",
    ).length;
    const deletions = this.events.filter(
      (e) => e.action === "service_deleted",
    ).length;
    const successful = this.events.filter((e) => e.success).length;

    return {
      totalActions: total,
      approvals,
      rejections,
      deletions,
      successRate: total > 0 ? (successful / total) * 100 : 0,
    };
  }

  /**
   * Get recent events (last 10)
   */
  static getRecentEvents(): ServiceActionEvent[] {
    return this.events.slice(-10);
  }

  /**
   * Clear analytics data (useful for testing)
   */
  static clear(): void {
    this.events = [];
  }
}
