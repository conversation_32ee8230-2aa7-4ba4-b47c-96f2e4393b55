"use client";

import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  Button,
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui";
import { Menu, LogOut } from "lucide-react";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import { MenuItem } from "./account-dropdown-menu-item";
import { logoutUser } from "@/actions/auth";

export function AccountDropdown() {
  const t = useTranslations("Header");

  const handleSignOut = async () => {
    try {
      await logoutUser();
      toast.success("Logged out successfully");
    } catch (_error) {
      toast.error("Failed to logout. Please try again.");
    }
  };

  // For now, we'll assume user is always signed in since this dropdown
  // should only appear for authenticated users
  const getUserInitials = () => "U"; // TODO: Get from actual user data

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="h-auto px-3 py-1.5 gap-2 border-input bg-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <Menu className="h-4 w-4" />
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder-avatar.jpg" alt="User avatar" />
            <AvatarFallback className="text-sm bg-primary text-primary-foreground font-medium">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-56">
        <div className="flex flex-col gap-1 p-2">
          <MenuItem
            onClick={handleSignOut}
            icon={<LogOut className="mr-2 h-4 w-4" />}
            label={t("signOut")}
          />
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
