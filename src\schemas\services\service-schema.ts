import { z } from "zod";
import { ProviderSchema } from "./provider-schema";

// Category Schema
export const CategorySchema = z.object({
  id: z.number(),
  intl_id: z.string(),
  name: z.string(),
});

// Pricing Schema
export const PricingSchema = z.object({
  price: z.number(),
  priceEuro: z.number().nullable(),
  iso_curry: z.string(),
  pricingModel: z.string(),
});

// Service Base Schema (for creation/editing)
export const ServiceBaseSchema = z.object({
  name: z.string(),
  description: z.string(),
  longDescription: z.string().optional(),
  categories: z.array(z.string()),
  locations: z.array(z.string()).optional(),
  isPublished: z.boolean().optional(),
});

// Main Service Schema (updated to match actual API response)
export const ServiceSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string(),
  longDescription: z.string(),
  isPublished: z.boolean(),
  isLiked: z.number().optional(), // Optional in API response
  categories: z.array(z.string()), // API returns strings, not objects
  provider: ProviderSchema,
  img: z.array(z.string()),
  pricing: PricingSchema,
  locations: z.array(z.string()),
  createdAt: z.string().nullable(),
  updatedAt: z.string().nullable(),
  status: z.string(),
  rejectionReason: z.string().nullable().optional(), // Optional in API response
});

// Service Pricing Request Schema
export const ServicePricingRequestSchema = z.object({
  price: z.number(),
  iso_curry: z.string().max(3),
  pricingModel: z.string(),
});

// Create Service Request Schema
export const CreateServiceRequestSchema = z.object({
  images: z.array(z.any()).optional(),
  service: ServiceBaseSchema.extend({
    pricing: ServicePricingRequestSchema.optional(),
    provider_id: z.string().optional(),
  }),
});

// Edit Service Request Schema
export const EditServiceRequestSchema = z.object({
  images: z.array(z.any()).optional(),
  service: ServiceBaseSchema.extend({
    id: z.number(),
    pricing: ServicePricingRequestSchema.optional(),
    provider_id: z.string().optional(),
    img: z.array(z.string()).optional(),
    status: z.string().optional(),
    rejectionReason: z.string().optional(),
  }),
});

// Validate Service Params Schema
export const ValidateServiceParamsSchema = z.object({
  serviceId: z.number(),
  isPublished: z.boolean(),
});

// Delete Service Params Schema
export const DeleteServiceParamsSchema = z.object({
  id: z.number(),
});

// Reject Service Params Schema
export const RejectServiceParamsSchema = z.object({
  serviceId: z.number(),
  reason: z.string().min(1, "Rejection reason is required"),
});
