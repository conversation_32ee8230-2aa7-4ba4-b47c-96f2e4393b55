import { z } from "zod";

// Provider Contact Schema
export const ProviderContactSchema = z.object({
  phone_number: z.string().optional(),
  address_email: z.string().optional(),
  phone_number_country_code: z.string().optional(),
});

// Provider Address Schema
export const ProviderAddressSchema = z.object({
  city: z.string().optional(),
  country: z.string().optional(),
});

// Provider Unsafe Metadata Schema
export const ProviderUnsafeMetadataSchema = z.object({
  title: z.string().optional(),
  about_me: z.string().optional(),
  currency: z.string().optional(),
  language: z.string().optional(),
  username: z.string().optional(),
  phone_number: z.string().optional(),
  address_email: z.string().optional(),
  date_of_birth: z.string().optional(),
  notifications: z.array(z.string()).optional(),
  postal_address: ProviderAddressSchema.optional(),
  contact_address: ProviderContactSchema.optional(),
});

// Main Provider Schema (updated to match actual API response)
export const ProviderSchema = z.object({
  id: z.number(),
  extl_id: z.string(),
  note: z.string().nullable(),
  status: z.string(),
  firstName: z.string().optional(), // Optional in API response
  lastName: z.string().optional(), // Optional in API response
  imageUrl: z.string().nullable().optional(), // Optional in API response
  email: z.string().nullable().optional(), // Optional in API response
  unsafe_metadata: ProviderUnsafeMetadataSchema.nullable().optional(),
});
