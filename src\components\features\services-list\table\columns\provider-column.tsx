import { TruncatedText } from "@/components/common";

export const providerColumn: NativeTableColumn<Service> = {
  id: "name",
  accessorKey: "name",
  header: "Fournisseur",
  enableSorting: true,
  cell: ({ value }) => {
    const provider = value as string;
    const fullName = provider || "Unknown User";

    return (
      <div className="text-sm">
        <div className="font-medium">
          <TruncatedText maxWidth="150" text={fullName} />
        </div>
      </div>
    );
  },
  size: 160,
};
