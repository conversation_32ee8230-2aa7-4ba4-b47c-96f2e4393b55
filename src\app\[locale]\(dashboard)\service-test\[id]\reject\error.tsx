"use client";

import { ServiceTestError } from "@/components/features/service-test";
import { useParams } from "next/navigation";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Error page for service test reject action
 * Handles errors that occur during service rejection
 */
export default function ServiceTestRejectErrorPage({ error }: ErrorPageProps) {
  const params = useParams();
  const serviceId = params?.id as string;

  return <ServiceTestError error={error} serviceId={serviceId} />;
}
