import { getServiceDetailAction } from "@/actions";
import { getGlobalQueryParams } from "@/lib";
import { notFound } from "next/navigation";
import { ServiceTest } from "./service-test";
import { SearchParams } from "nuqs";
import { GlobalQueryParamsType } from "@/types/global";

interface AsyncServiceTestProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

/**
 * Async component for server-side service test data fetching
 * Follows the established async/sync component separation pattern
 * Uses the same data fetching as service details but with improved presentation
 */
export async function AsyncServiceTest({
  params,
  searchParams,
}: AsyncServiceTestProps) {
  const { id } = await params;
  const query = await getGlobalQueryParams(searchParams);

  try {
    const response = await getServiceDetailAction(
      id,
      query as unknown as GlobalQueryParamsType,
    );

    if (!response.response || response.status === 404) {
      return notFound();
    }

    return <ServiceTest service={response.response} />;
  } catch (error) {
    throw error; // Let error boundary handle it
  }
}
