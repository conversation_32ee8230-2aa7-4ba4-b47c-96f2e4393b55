import { useTranslations } from "next-intl";

interface ServiceTestPricingDetailsProps {
  pricing: ServicePricing;
}

/**
 * Pricing details component for service test
 * Displays service pricing information with improved formatting
 * Follows clean code principles with clear price presentation
 */
export function ServiceTestPricingDetails({ pricing }: ServiceTestPricingDetailsProps) {
  const t = useTranslations("ServiceDetails");

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined) return t("notAvailable");
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-foreground">
        {t("pricingDetails")}
      </h3>
      <div className="bg-muted/50 border border-border rounded-lg px-4 py-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Base Price */}
          <div className="space-y-1">
            <span className="text-xs text-muted-foreground">
              {t("basePrice")}
            </span>
            <div className="text-sm font-medium text-foreground">
              {formatPrice(pricing.basePrice)}
            </div>
          </div>

          {/* Hourly Rate */}
          {pricing.hourlyRate && (
            <div className="space-y-1">
              <span className="text-xs text-muted-foreground">
                {t("hourlyRate")}
              </span>
              <div className="text-sm font-medium text-foreground">
                {formatPrice(pricing.hourlyRate)} / {t("hour")}
              </div>
            </div>
          )}

          {/* Currency */}
          <div className="space-y-1">
            <span className="text-xs text-muted-foreground">
              {t("currency")}
            </span>
            <div className="text-sm font-medium text-foreground">
              {pricing.currency || "USD"}
            </div>
          </div>

          {/* Pricing Type */}
          {pricing.type && (
            <div className="space-y-1">
              <span className="text-xs text-muted-foreground">
                {t("pricingType")}
              </span>
              <div className="text-sm font-medium text-foreground">
                {pricing.type}
              </div>
            </div>
          )}
        </div>

        {/* Additional Notes */}
        {pricing.notes && (
          <div className="mt-4 pt-4 border-t border-border">
            <span className="text-xs text-muted-foreground">
              {t("pricingNotes")}
            </span>
            <p className="text-sm text-foreground mt-1">
              {pricing.notes}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
