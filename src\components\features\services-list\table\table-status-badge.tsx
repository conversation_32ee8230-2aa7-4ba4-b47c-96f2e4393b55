import { useTranslations } from "next-intl";

interface TableStatusBadgeProps {
  status: string;
}

interface StatusConfig {
  label: string;
  className: string;
}

// Type for translation function - more flexible to work with useTranslations
type TranslationFunction = (key: string) => string;

// Helper functions to reduce complexity - Updated for API status values
const getActiveConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusActive"),
  className: "bg-green-100 text-green-800",
});

const getInactiveConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusInactive"),
  className: "bg-gray-100 text-gray-800",
});

const getPendingConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusPending"),
  className: "bg-yellow-100 text-yellow-800",
});

const getSuspendedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusSuspended"),
  className: "bg-red-100 text-red-800",
});

const getApprovedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusApproved"),
  className: "bg-blue-100 text-blue-800",
});

const getRejectedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusRejected"),
  className: "bg-red-100 text-red-800",
});

// Legacy status configs for backward compatibility
const getPublishedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusPublished"),
  className: "bg-green-100 text-green-800",
});

const getRefusedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusRefused"),
  className: "bg-red-100 text-red-800",
});

const getHiddenConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusHidden"),
  className: "bg-gray-100 text-gray-800",
});

const getDeletedConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusDeleted"),
  className: "bg-red-100 text-red-800",
});

const getDisabledConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusDisabled"),
  className: "bg-gray-100 text-gray-800",
});

const getUnknownConfig = (t: TranslationFunction): StatusConfig => ({
  label: t("statusUnknown"),
  className: "bg-gray-100 text-gray-800",
});

// Status mapping to reduce complexity - Updated for API status values
const STATUS_MAPPING: Record<string, (t: TranslationFunction) => StatusConfig> =
  {
    // API Status Values (uppercase)
    ACTIVE: getActiveConfig,
    INACTIVE: getInactiveConfig,
    PENDING: getPendingConfig,
    SUSPENDED: getSuspendedConfig,
    APPROVED: getApprovedConfig,
    REJECTED: getRejectedConfig,

    // API Status Values (lowercase)
    active: getActiveConfig,
    inactive: getInactiveConfig,
    pending: getPendingConfig,
    suspended: getSuspendedConfig,
    approved: getApprovedConfig,
    rejected: getRejectedConfig,

    // Legacy status values for backward compatibility
    published: getPublishedConfig,
    publié: getPublishedConfig,
    refused: getRefusedConfig,
    refusé: getRefusedConfig,
    hidden: getHiddenConfig,
    masqué: getHiddenConfig,
    deleted: getDeletedConfig,
    supprimé: getDeletedConfig,
    "en attente": getPendingConfig,
    disabled: getDisabledConfig,
    désactivé: getDisabledConfig,
  };

const getStatusConfig = (
  status: string,
  t: TranslationFunction,
): StatusConfig => {
  // Try exact match first (for API uppercase values)
  let configFunction = STATUS_MAPPING[status];

  // If no exact match, try lowercase
  if (!configFunction) {
    const normalizedStatus = status.toLowerCase();
    configFunction = STATUS_MAPPING[normalizedStatus];
  }

  return configFunction ? configFunction(t) : getUnknownConfig(t);
};

export function TableStatusBadge({ status }: TableStatusBadgeProps) {
  const t = useTranslations("Services");

  // Create a wrapper function that matches our TranslationFunction type
  // We use type assertion to work around the strict typing of useTranslations
  const translationWrapper: TranslationFunction = (key: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (t as any)(key);
  };

  const config = getStatusConfig(status, translationWrapper);

  return (
    <span
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${config.className}`}
    >
      {config.label}
    </span>
  );
}
