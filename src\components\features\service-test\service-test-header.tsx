import { useTranslations } from "next-intl";
import { ChevronLeft } from "lucide-react";

interface ServiceTestHeaderProps {
  onBack: () => void;
}

/**
 * Header component for service test page
 * Provides navigation back to services list
 * Follows clean design principles with clear visual hierarchy
 */
export function ServiceTestHeader({ onBack }: ServiceTestHeaderProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <div className="flex justify-between items-center w-full gap-14 px-2.5 py-2">
      <div className="flex items-center gap-2.5">
        <button
          onClick={onBack}
          className="flex justify-center items-center w-9 h-9 bg-background border border-border rounded-full hover:bg-muted transition-colors"
          aria-label={t("backToServices")}
        >
          <ChevronLeft className="w-4 h-4 text-foreground" />
        </button>
        <span className="text-sm text-foreground font-medium">
          {t("back")}
        </span>
      </div>
      
      {/* Spacer for center alignment */}
      <div className="w-6 h-6" />
    </div>
  );
}
