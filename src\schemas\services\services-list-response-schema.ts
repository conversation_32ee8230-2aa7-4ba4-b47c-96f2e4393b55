import { z } from "zod";
import { ServiceSchema } from "./service-schema";
import { SupportSchema } from "./support-schema";
import { PaginatedResponseSchema } from "./pagination-schema";

// API Services List Response Schema (matches actual API structure)
export const ServicesListResponseSchema = z.object({
  services: z.array(ServiceSchema), // Use updated ServiceSchema
  total: z.number(),
  page: z.coerce.number(), // Convert string to number
  limit: z.coerce.number(), // Convert string to number
  totalPages: z.number(),
});

// Legacy schema for backward compatibility
export const LegacyServicesListResponseSchema = PaginatedResponseSchema.extend({
  items: z.array(ServiceSchema),
});

// Service Detail Response Schema

// User Services Response Schema
export const UserServicesResponseSchema = z.array(ServiceSchema);

// Support Detail Response Schema

// Supports List Response Schema
export const SupportsListResponseSchema = PaginatedResponseSchema.extend({
  items: z.array(SupportSchema),
});

export { ServiceSchema as ServiceDetailResponseSchema } from "./service-schema";
export { SupportSchema as SupportDetailResponseSchema } from "./support-schema";
