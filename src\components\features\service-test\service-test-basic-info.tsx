import { useTranslations } from "next-intl";

interface ServiceTestBasicInfoProps {
  service: Service;
}

/**
 * Basic information component for service test
 * Displays creation date with improved styling
 * Follows clean code principles with clear formatting
 */
export function ServiceTestBasicInfo({ service }: ServiceTestBasicInfoProps) {
  const t = useTranslations("ServiceDetails");

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t("notAvailable");
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return t("invalidDate");
    }
  };

  return (
    <div className="space-y-2">
      <h3 className="text-sm font-medium text-foreground">
        {t("createdAt")}
      </h3>
      <div className="bg-muted/50 border border-border rounded-lg px-4 py-3">
        <span className="text-sm text-foreground">
          {formatDate(service.createdAt)}
        </span>
      </div>
    </div>
  );
}
