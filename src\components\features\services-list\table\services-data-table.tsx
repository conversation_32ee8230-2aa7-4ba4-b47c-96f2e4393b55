"use client";

import { Table } from "@/components/ui/table";
import { useServerSideTable } from "@/hooks/use-server-side-table";
import { ServicesTableBody } from "./components/table-body";
import { DataTableHeader } from "./components/table-header";
import { TableFilters } from "./filters/table-filters";
import { TablePagination } from "./pagination/table-pagination";

interface ServicesDataTableProps {
  columns: NativeTableColumn<Service>[];
  data: Service[];
  totalItems: number;
  totalPages: number;
  isLoading?: boolean;
}

export function ServicesDataTable({
  columns,
  data,
  totalItems,
  totalPages,
  isLoading = false,
}: ServicesDataTableProps) {
  // Use server-side table hook for API-driven filtering and sorting
  const table = useServerSideTable({ data, columns, isLoading });

  return (
    <div className="bg-white rounded-[10px] border border-secondary overflow-hidden">
      <TableFilters table={table} />

      <Table>
        <DataTableHeader table={table} />
        <ServicesTableBody table={table} columns={columns} />
      </Table>

      <TablePagination totalItems={totalItems} totalPages={totalPages} />
    </div>
  );
}
