import { routing } from "@/i18n/routing";
import { cn } from "@/lib";
import { QueryProvider } from "@/providers";
import type { Metadata } from "next";
import { NextIntlClientProvider, hasLocale } from "next-intl";
// import { Poppins } from "next/font/google";
import { notFound } from "next/navigation";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import "./globals.css";

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

// const poppins = Poppins({
//   weight: ["300", "400", "500", "600", "700"],
//   subsets: ["latin"],
// });

export const metadata: Metadata = {
  title: "Afreeserv",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
  params,
}: RootLayoutProps) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  return (
    <html lang={locale} suppressHydrationWarning>
      <body
        className={cn(
          `antialiased`,
          // poppins.className
        )}
        suppressHydrationWarning
      >
        <NextIntlClientProvider>
          <NuqsAdapter>
            <QueryProvider>{children}</QueryProvider>
          </NuqsAdapter>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
