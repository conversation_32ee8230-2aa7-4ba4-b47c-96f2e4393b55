"use client";

import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ServiceTestBasicInfo } from "./service-test-basic-info";
import { ServiceTestDescriptions } from "./service-test-descriptions";
import { ServiceTestCategoriesAndLocations } from "./service-test-categories-and-locations";
import { ServiceTestStatusAndDates } from "./service-test-status-and-dates";
import { ServiceTestImages } from "./service-test-images";
import { ServiceTestPricingDetails } from "./service-test-pricing-details";
import { ServiceTestActionButtons } from "./service-test-action-buttons";

interface ServiceTestInformationProps {
  service: Service;
}

/**
 * Main service information component for service test
 * Organizes all service details with improved layout and design
 * Follows clean code principles with component composition
 */
export function ServiceTestInformation({
  service,
}: ServiceTestInformationProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <Card className="w-full bg-background border border-border">
      {/* Status Header */}
      <CardHeader className="border-b border-border">
        <ServiceTestStatusAndDates service={service} />
      </CardHeader>

      {/* Main Content */}
      <CardContent className="p-6">
        {/* Two Column Layout: Descriptions Left, Images Right */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Descriptions and Details */}
          <div className="space-y-6">
            {/* Basic Information */}
            <ServiceTestBasicInfo service={service} />

            {/* Descriptions */}
            <ServiceTestDescriptions service={service} />

            {/* Categories and Locations */}
            <ServiceTestCategoriesAndLocations service={service} />

            {/* Pricing Details */}
            <ServiceTestPricingDetails pricing={service.pricing} />

            {/* About Provider Section */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-foreground">
                {t("aboutProviderLabel")}
              </h3>
              <div className="bg-muted/50 border border-border rounded-lg p-4">
                <p className="text-sm text-foreground leading-relaxed">
                  {service.provider.unsafe_metadata?.about_me ||
                    t("notAvailable")}
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Images */}
          <div className="space-y-6">
            <ServiceTestImages
              images={service.img}
              serviceName={service.name}
            />
          </div>
        </div>

        {/* Action Buttons - Full Width at Bottom */}
        <div className="pt-8 mt-8 border-t border-border">
          <ServiceTestActionButtons service={service} />
        </div>
      </CardContent>
    </Card>
  );
}
