import { getServiceDetailAction } from "@/actions";
import { getGlobalQueryParams } from "@/lib";
import { notFound } from "next/navigation";
import { ServiceTestDelete } from "./service-test-delete";
import { SearchParams } from "nuqs";
import { GlobalQueryParamsType } from "@/types/global";

interface AsyncServiceTestDeleteProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

/**
 * Async component for service test delete page
 * Fetches service data and renders the delete confirmation
 * Follows the established async/sync component separation pattern
 */
export async function AsyncServiceTestDelete({
  params,
  searchParams,
}: AsyncServiceTestDeleteProps) {
  const { id } = await params;
  const query = await getGlobalQueryParams(searchParams);

  try {
    const response = await getServiceDetailAction(
      id,
      query as unknown as GlobalQueryParamsType,
    );

    if (!response.response || response.status === 404) {
      return notFound();
    }

    return <ServiceTestDelete service={response.response} />;
  } catch (error) {
    throw error; // Let error boundary handle it
  }
}
