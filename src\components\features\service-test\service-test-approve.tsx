"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, CheckCircle, AlertTriangle } from "lucide-react";
import { handleApprove as approveAction } from "@/actions/services";
import { toast } from "sonner";

interface ServiceTestApproveProps {
  service: Service;
}

/**
 * Service test approve page component
 * Provides a dedicated page for approving services with confirmation
 * Follows clean code principles with clear status validation
 */
export function ServiceTestApprove({ service }: ServiceTestApproveProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleBack = () => {
    router.push(`/service-test/${service.id}`);
  };

  const isAlreadyApproved = service.status === "APPROVED" || service.status === "ACTIVE";

  const handleSubmit = async () => {
    if (isAlreadyApproved) {
      toast.info(t("serviceAlreadyApproved"));
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Service approval started:", {
        serviceId: service.id,
        serviceName: service.name,
        timestamp: new Date().toISOString(),
      });

      const result = await approveAction({ serviceId: service.id });

      if (result.status >= 200 && result.status < 300) {
        toast.success(t("serviceApprovedSuccessfully"));
        console.log("Service approved successfully:", {
          serviceId: service.id,
          serviceName: service.name,
          timestamp: new Date().toISOString(),
        });
        
        // Navigate back to service details
        router.push(`/service-test/${service.id}`);
      } else {
        throw new Error("Failed to approve service");
      }
    } catch (error) {
      console.error("Error approving service:", error);
      toast.error(error instanceof Error ? error.message : t("approvalFailed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={handleBack}
          className="rounded-full"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {t("approveService")}
          </h1>
          <p className="text-sm text-muted-foreground">
            {service.name}
          </p>
        </div>
      </div>

      {/* Approval Confirmation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-600">
            <CheckCircle className="w-5 h-5" />
            {t("serviceApproval")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Service Summary */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("currentStatus")}:</span>
              <Badge variant={isAlreadyApproved ? "default" : "secondary"}>
                {service.status}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("serviceName")}:</span>
              <span className="text-sm font-medium">{service.name}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">{t("provider")}:</span>
              <span className="text-sm font-medium">
                {service.provider.first_name} {service.provider.last_name}
              </span>
            </div>
          </div>

          {/* Warning for already approved services */}
          {isAlreadyApproved && (
            <div className="flex items-center gap-2 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-yellow-600" />
              <p className="text-sm text-yellow-800">
                {t("serviceAlreadyApprovedWarning")}
              </p>
            </div>
          )}

          {/* Confirmation Message */}
          {!isAlreadyApproved && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-800">
                {t("approvalConfirmationMessage")}
              </p>
            </div>
          )}

          <div className="flex gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={isSubmitting}
              className="flex-1"
            >
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || isAlreadyApproved}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? t("approving") : t("approveService")}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
