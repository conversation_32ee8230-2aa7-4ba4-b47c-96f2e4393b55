import { useTranslations } from "next-intl";

interface ServiceActionButtonsProps {
  service: Service;
  onDelete: () => void;
  onReject: () => void;
  onApprove: () => void;
  isDeleting: boolean;
  isRejecting: boolean;
  isApproving: boolean;
}

// Helper components to reduce complexity
interface ActionButtonProps {
  onClick: () => void;
  disabled: boolean;
  children: React.ReactNode;
}

function ActionButton({ onClick, disabled, children }: ActionButtonProps) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className="w-[200px] h-[35px] bg-secondary rounded-[10px] flex items-center justify-center hover:bg-secondary/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <span className="text-xs font-['Poppins'] text-muted-foreground uppercase">
        {children}
      </span>
    </button>
  );
}

function DeleteButton({
  onDelete,
  isDeleting,
  isDisabled,
  t,
}: {
  onDelete: () => void;
  isDeleting: boolean;
  isDisabled: boolean;
  t: ReturnType<typeof useTranslations>;
}) {
  return (
    <ActionButton onClick={onDelete} disabled={isDeleting || isDisabled}>
      {isDeleting ? "Deleting..." : t("delete")}
    </ActionButton>
  );
}

function RejectButton({
  service,
  onReject,
  isRejecting,
  isDisabled,
  t,
}: {
  service: Service;
  onReject: () => void;
  isRejecting: boolean;
  isDisabled: boolean;
  t: ReturnType<typeof useTranslations>;
}) {
  if (service.status === "REJECTED") return null;

  return (
    <ActionButton onClick={onReject} disabled={isRejecting || isDisabled}>
      {isRejecting ? "Rejecting..." : t("reject")}
    </ActionButton>
  );
}

function ApproveButton({
  service,
  onApprove,
  isApproving,
  isDisabled,
  t,
}: {
  service: Service;
  onApprove: () => void;
  isApproving: boolean;
  isDisabled: boolean;
  t: ReturnType<typeof useTranslations>;
}) {
  if (service.status === "APPROVED" || service.status === "ACTIVE") return null;

  return (
    <ActionButton onClick={onApprove} disabled={isApproving || isDisabled}>
      {isApproving ? "Approving..." : t("validate")}
    </ActionButton>
  );
}

export function ServiceActionButtons({
  service,
  onDelete,
  onReject,
  onApprove,
  isDeleting,
  isRejecting,
  isApproving,
}: ServiceActionButtonsProps) {
  const t = useTranslations("ServiceDetails");
  const isAnyActionInProgress = isDeleting || isRejecting || isApproving;

  return (
    <div className="flex justify-center items-center gap-12 px-5 py-2.5">
      <DeleteButton
        onDelete={onDelete}
        isDeleting={isDeleting}
        isDisabled={isAnyActionInProgress}
        t={t}
      />
      <RejectButton
        service={service}
        onReject={onReject}
        isRejecting={isRejecting}
        isDisabled={isAnyActionInProgress}
        t={t}
      />
      <ApproveButton
        service={service}
        onApprove={onApprove}
        isApproving={isApproving}
        isDisabled={isAnyActionInProgress}
        t={t}
      />
    </div>
  );
}
