import { getServiceDetailAction } from "@/actions";
import { getGlobalQueryParams } from "@/lib";
import { notFound } from "next/navigation";
import { ServiceTestApprove } from "./service-test-approve";
import { SearchParams } from "nuqs";
import { GlobalQueryParamsType } from "@/types/global";

interface AsyncServiceTestApproveProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

/**
 * Async component for service test approve page
 * Fetches service data and renders the approve confirmation
 * Follows the established async/sync component separation pattern
 */
export async function AsyncServiceTestApprove({
  params,
  searchParams,
}: AsyncServiceTestApproveProps) {
  const { id } = await params;
  const query = await getGlobalQueryParams(searchParams);

  try {
    const response = await getServiceDetailAction(
      id,
      query as unknown as GlobalQueryParamsType,
    );

    if (!response.response || response.status === 404) {
      return notFound();
    }

    return <ServiceTestApprove service={response.response} />;
  } catch (error) {
    throw error; // Let error boundary handle it
  }
}
