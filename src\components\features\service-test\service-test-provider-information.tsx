import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface ServiceTestProviderInformationProps {
  name: string;
  provider: Provider;
}

/**
 * Provider information component for service test
 * Displays provider details with improved design
 * Follows clean code principles with single responsibility
 */
export function ServiceTestProviderInformation({
  name,
  provider,
}: ServiceTestProviderInformationProps) {
  const t = useTranslations("ServiceDetails");

  const getProviderInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0)?.toUpperCase() || "";
    const last = lastName?.charAt(0)?.toUpperCase() || "";
    return first + last || "P";
  };

  const getProviderDisplayName = () => {
    if (provider.first_name && provider.last_name) {
      return `${provider.first_name} ${provider.last_name}`;
    }
    return provider.email || t("unknownProvider");
  };

  return (
    <Card className="w-full bg-background border border-border">
      <CardContent className="p-6">
        <div className="flex items-center gap-4">
          <Avatar className="w-16 h-16">
            <AvatarImage 
              src={provider.profile_picture_url} 
              alt={getProviderDisplayName()}
            />
            <AvatarFallback className="bg-muted text-foreground text-lg font-medium">
              {getProviderInitials(provider.first_name, provider.last_name)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex flex-col gap-1">
            <h2 className="text-xl font-semibold text-foreground">
              {name}
            </h2>
            <p className="text-sm text-muted-foreground">
              {t("providedBy")} {getProviderDisplayName()}
            </p>
            {provider.phone && (
              <p className="text-xs text-muted-foreground">
                {provider.phone}
              </p>
            )}
          </div>
        </div>
        
        {provider.unsafe_metadata?.about_me && (
          <div className="mt-4 pt-4 border-t border-border">
            <h3 className="text-sm font-medium text-foreground mb-2">
              {t("aboutProvider")}
            </h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {provider.unsafe_metadata.about_me}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
