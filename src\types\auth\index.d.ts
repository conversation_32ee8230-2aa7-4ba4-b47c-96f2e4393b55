import {
  // Authentication schemas
  FormLoginSchema,
  FormAdminRegistrationSchema,
} from "@/schemas";
import { z } from "zod";

// Export empty to make this a module
export {};

declare global {
  // ============================================================================
  // FORM DATA TYPES (from Zod schemas)
  // ============================================================================

  /**
   * Login form data type
   * Inferred from FormLoginSchema (email + password)
   */
  type LoginFormData = z.infer<typeof FormLoginSchema>;

  /**
   * Admin registration form data type
   * Inferred from FormAdminRegistrationSchema
   */
  type AdminRegistrationFormData = z.infer<typeof FormAdminRegistrationSchema>;

  // ============================================================================
  // API TYPES (matching API specification exactly)
  // ============================================================================

  /**
   * Admin login request type (matches AdminLoginDTO from API)
   * Used for POST /admin/auth/login requests
   */
  type AdminLoginRequest = {
    email: string;
    password: string;
  };

  /**
   * Admin status enumeration (from API specification)
   */
  type AdminStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED";

  /**
   * Complete admin type (matches AdminDTO from API)
   * Contains all fields returned by the API
   */
  type Admin = {
    id: number; // API returns number, not string
    email: string;
    firstName: string;
    lastName: string;
    fullName: string; // Computed field from API
    avatar: string | null; // Avatar URL (can be null)
    status: AdminStatus; // Enum type
    lastLoginAt: string; // ISO date string
    roles: string[]; // Array of role names
    permissions: string[]; // Array of permission strings
  };

  /**
   * Authentication response type (matches AuthResponseDTO from API)
   * Returned by POST /admin/auth/login
   */
  type AuthResponse = {
    accessToken: string; // JWT access token
    refreshToken: string; // JWT refresh token
    admin: Admin; // Complete admin object
    expiresAt: string; // ISO date string
  };

  // ============================================================================
  // ROLE & PERMISSION TYPES
  // ============================================================================

  /**
   * Available admin roles
   */
  type AdminRole = "super_admin" | "admin" | "moderator" | "support";

  /**
   * Role definition type
   * Contains metadata about each role
   */
  type RoleDefinition = {
    name: AdminRole;
    level: number; // 1 = highest privilege, 4 = lowest
    description: string;
    defaultPermissions: string[];
  };

  /**
   * Access control options for route and component protection
   */
  type AccessControlOptions = {
    roles?: AdminRole[];
    permissions?: string[];
    requireAll?: boolean; // true = AND logic, false = OR logic
  };

  // ============================================================================
  // API RESPONSE TYPES
  // ============================================================================

  /**
   * Generic API response wrapper
   * Used for consistent error handling
   */
  type ApiResponse<T = unknown> = {
    success: boolean;
    data?: T;
    error?: string;
    statusCode?: number;
  };

  /**
   * API error response structure
   * Matches the error format from API specification
   */
  type ApiErrorResponse = {
    statusCode: number;
    message: string;
    error: string;
  };

  /**
   * Login response wrapper for client-side handling
   */
  type LoginResponse = {
    success: boolean;
    data?: {
      admin: Admin;
      accessToken: string;
      refreshToken: string;
      expiresAt: string;
      message: string;
    };
    error?: {
      code: string;
      message: string;
      details?: unknown;
    };
  };

  /**
   * Logout response type
   */
  type LogoutResponse = {
    success: boolean;
    message?: string;
    error?: {
      code: string;
      message: string;
    };
  };

  /**
   * Token validation response type
   * Used when validating tokens with /admin/auth/profile
   */
  type TokenValidationResponse = {
    valid: boolean;
    admin?: Admin;
    error?: string;
  };

  /**
   * Client-side authentication state
   * Used by useAuth hook for state management
   */
  interface AuthState {
    isAuthenticated: boolean;
    admin: Admin | null;
    accessToken: string | null;
    refreshToken: string | null;
    expiresAt: string | Date | null;
    isLoading: boolean;
    error: string | null;
  }

  // ============================================================================
  // AUTHENTICATION STATE TYPES
  // ============================================================================

  /**
   * Authentication state type for client-side state management
   * Used in React contexts and hooks
   */
  type AuthState = {
    isAuthenticated: boolean;
    admin: Admin | null;
    accessToken: string | null;
    refreshToken: string | null;
    expiresAt: string | null;
    isLoading: boolean;
    error: string | null;
  };

  /**
   * JWT token payload type (for token decoding)
   */
  type TokenPayload = {
    sub: string; // Subject (admin ID)
    email: string;
    roles: AdminRole[];
    permissions: string[];
    iat: number; // Issued at
    exp: number; // Expires at
  };

  // ============================================================================
  // SERVICE INTERFACE TYPES
  // ============================================================================

  /**
   * Login credentials type for service layer
   * Matches API requirements exactly
   */
  type LoginCredentials = {
    email: string; // Required email field for API authentication
    password: string;
  };

  /**
   * Token validation response type for service layer
   * Used by auth service validateToken method
   */
  type ValidateTokenResponse = {
    valid: boolean;
    admin?: Admin; // Use Admin type directly from API
    error?: {
      code: string;
      message: string;
      details?: unknown;
    };
  };
}
