import { <PERSON>, Card<PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft } from "lucide-react";

/**
 * Loading skeleton for service test reject page
 * Provides visual feedback while data is being fetched
 * Matches the layout structure of the reject form
 */
export function ServiceTestRejectSkeleton() {
  return (
    <div className="container mx-auto py-6 max-w-2xl">
      {/* Header Skeleton */}
      <div className="flex items-center gap-4 mb-6">
        <div className="flex justify-center items-center w-10 h-10 bg-background border border-border rounded-full">
          <ChevronLeft className="w-4 h-4 text-foreground" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-32" />
        </div>
      </div>

      {/* Form Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-3 w-48" />
          </div>

          <div className="flex gap-4 pt-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 flex-1" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
