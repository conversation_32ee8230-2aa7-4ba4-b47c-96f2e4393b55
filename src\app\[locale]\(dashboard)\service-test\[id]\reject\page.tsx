import {
  AsyncServiceTestReject,
  ServiceTestRejectSkeleton,
} from "@/components/features/service-test";
import { SearchParams } from "nuqs";
import { Suspense } from "react";

interface ServiceTestRejectPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

export default function ServiceTestRejectPage({
  params,
  searchParams,
}: ServiceTestRejectPageProps) {
  return (
    <div className="flex flex-col gap-2.5 py-2.5 px-5">
      <Suspense fallback={<ServiceTestRejectSkeleton />}>
        <AsyncServiceTestReject params={params} searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
