"use client";

import { ServicesTableContent } from "./services-table-content";
import { ServicesTableHeader } from "./services-table-header";
import { HTMLAttributes } from "react";

interface ServicesTableProps extends HTMLAttributes<HTMLDivElement> {
  services: Service[];
  totalItems: number;
  isLoading?: boolean;
}

/**
 * Services table component for displaying services data
 * Follows the established async/sync component separation pattern
 */
export function ServicesTable(props: ServicesTableProps) {
  const {
    services = [],
    totalItems = 0,
    isLoading = false,
    className,
    ...rest
  } = props;

  return (
    <div className={className} {...rest}>
      <ServicesTableHeader current={services?.length || 0} total={totalItems} />

      <ServicesTableContent
        services={services}
        totalItems={totalItems}
        totalPages={Math.ceil(totalItems / 10)} // Default page size
        isLoading={isLoading}
      />
    </div>
  );
}
