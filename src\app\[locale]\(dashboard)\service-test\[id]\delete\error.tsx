"use client";

import { ServiceTestError } from "@/components/features/service-test";
import { useParams } from "next/navigation";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Error page for service test delete action
 * Handles errors that occur during service deletion
 */
export default function ServiceTestDeleteErrorPage({ error }: ErrorPageProps) {
  const params = useParams();
  const serviceId = params?.id as string;

  return <ServiceTestError error={error} serviceId={serviceId} />;
}
