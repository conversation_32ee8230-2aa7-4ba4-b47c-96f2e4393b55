import { SearchIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface TableSearchFilterProps {
  table: {
    globalFilter: string;
    setGlobalFilter: (value: string) => void;
  };
}

export function TableSearchFilter({ table }: TableSearchFilterProps) {
  const t = useTranslations("Services");

  // Server-side search placeholder (searches across all fields)
  const placeholder = t("search") || "Search services...";

  return (
    <div
      className={cn(
        "flex items-center gap-2 relative bg-transparent transition-colors text-base rounded-md border border-input pl-3 h-9 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed md:text-sm has-[input:focus]:outline-none has-[input:focus]:ring-1 has-[input:focus]:ring-ring",
        "w-sm max-w-sm",
      )}
    >
      {/* Search icon */}
      <SearchIcon className="h-4 w-4 shrink-0 text-muted-foreground" />

      {/* Server-side search input (searches across title, description, and category) */}
      <input
        type="text"
        placeholder={placeholder}
        value={table.globalFilter}
        onChange={(event) => table.setGlobalFilter(event.target.value)}
        className="flex w-full border-none bg-transparent text-base transition-colors placeholder:text-muted-foreground outline-none h-9 py-1 p-0 leading-none md:text-sm"
      />
    </div>
  );
}
