"use client";

import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

interface ServiceTestErrorProps {
  error?: Error;
  serviceId?: string;
}

/**
 * Error component for service test page
 * Displays error information with navigation options
 * Follows clean code principles with clear error presentation
 */
export function ServiceTestError({
  error,
  serviceId,
}: ServiceTestErrorProps) {
  const t = useTranslations("ServiceDetails");
  const router = useRouter();

  const handleBack = () => {
    router.push("/services");
  };

  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] gap-6 p-8">
      <div className="flex items-center gap-3 text-destructive">
        <AlertCircle className="w-8 h-8" />
        <h2 className="text-xl font-semibold">{t("error")}</h2>
      </div>
      
      {serviceId && (
        <div className="text-sm text-muted-foreground">
          Service ID: {serviceId}
        </div>
      )}
      
      <div className="text-center max-w-md">
        <p className="text-sm text-muted-foreground">
          {error?.message || t("serviceNotFound")}
        </p>
      </div>

      <div className="flex gap-4">
        <Button onClick={handleRetry} variant="outline">
          {t("retry")}
        </Button>
        <Button onClick={handleBack}>
          {t("backToServices")}
        </Button>
      </div>
    </div>
  );
}
