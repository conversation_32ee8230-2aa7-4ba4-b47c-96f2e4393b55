import { useTranslations } from "next-intl";

interface ServiceTestDescriptionsProps {
  service: Service;
}

/**
 * Service descriptions component for service test
 * Displays service name and description with improved styling
 * Follows clean code principles with clear visual hierarchy
 */
export function ServiceTestDescriptions({ service }: ServiceTestDescriptionsProps) {
  const t = useTranslations("ServiceDetails");

  return (
    <div className="space-y-4">
      {/* Service Title */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          {t("serviceName")}
        </h3>
        <div className="bg-muted/50 border border-border rounded-lg px-4 py-3">
          <span className="text-sm text-foreground font-medium">
            {service.name}
          </span>
        </div>
      </div>

      {/* Service Description */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground">
          {t("longDescription")}
        </h3>
        <div className="bg-muted/50 border border-border rounded-lg px-4 py-3 min-h-[120px]">
          <p className="text-sm text-foreground leading-relaxed">
            {service.longDescription || service.description || t("notAvailable")}
          </p>
        </div>
      </div>
    </div>
  );
}
