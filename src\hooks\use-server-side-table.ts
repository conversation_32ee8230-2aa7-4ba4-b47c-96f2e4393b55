"use client";

import { useMemo } from "react";
import { useGlobalQueryParams } from "./use-global-query-params";

interface UseServerSideTableProps<TData> {
  data: TData[];
  columns: NativeTableColumn<TData>[];
  isLoading?: boolean;
}

/**
 * Server-side table hook that delegates all filtering, sorting, and searching to the backend API
 * This replaces the client-side useNativeTable hook for better performance and consistency
 *
 * Following clean code principles:
 * - Single Responsibility: Only handles table state and column management
 * - Dependency Inversion: Depends on URL state management abstraction
 * - Open/Closed: Open for extension with new column types, closed for modification
 */
export function useServerSideTable<TData = unknown>({
  data,
  columns,
  isLoading = false,
}: UseServerSideTableProps<TData>) {
  const { searchParams, setSearchParams } = useGlobalQueryParams();

  // Create column instances with server-side sorting methods
  const columnInstances = useMemo(() => {
    return columns.map((column) => ({
      ...column,

      // Server-side sorting methods
      getIsSorted: (): "asc" | "desc" | false => {
        // Map column IDs to API sort fields for comparison
        const sortFieldMap: Record<string, string> = {
          name: "title",
          title: "title",
          createdAt: "createdAt",
          updatedAt: "updatedAt",
          price: "price",
        };

        const apiSortField = sortFieldMap[column.id] || column.id;
        if (searchParams.sortBy !== apiSortField) return false;
        return searchParams.sortOrder === "DESC" ? "desc" : "asc";
      },

      toggleSorting: () => {
        if (column.enableSorting === false) return;

        const currentSort =
          searchParams.sortBy === column.id ? searchParams.sortOrder : null;

        // Map column IDs to API sort fields
        const sortFieldMap: Record<string, string> = {
          name: "title",
          title: "title",
          createdAt: "createdAt",
          updatedAt: "updatedAt",
          price: "price",
        };

        const apiSortField = sortFieldMap[column.id] || column.id;

        // Cycle through: none -> asc -> desc -> none
        if (!currentSort || currentSort === "DESC") {
          // Set to ascending
          setSearchParams({
            ...searchParams,
            sortBy: apiSortField,
            sortOrder: "ASC",
            next: 1, // Reset to first page when sorting changes
          });
        } else if (currentSort === "ASC") {
          // Set to descending
          setSearchParams({
            ...searchParams,
            sortBy: apiSortField,
            sortOrder: "DESC",
            next: 1,
          });
        } else {
          // Remove sorting
          const {
            sortBy: _sortBy,
            sortOrder: _sortOrder,
            ...restParams
          } = searchParams;
          setSearchParams({
            ...restParams,
            next: 1,
          });
        }
      },

      // Server-side filtering methods (for individual column filters if needed)
      getFilterValue: () => {
        switch (column.id) {
          case "status":
            return searchParams.status || "";
          case "categories":
            return searchParams.category || "";
          case "provider":
            return searchParams.providerId
              ? String(searchParams.providerId)
              : "";
          default:
            return "";
        }
      },

      setFilterValue: (value: unknown) => {
        if (column.enableFiltering === false) return;

        // Properly handle empty strings, null, undefined, and "all" values
        const normalizeValue = (val: unknown): string | undefined => {
          if (!val || val === "" || val === "all") return undefined;
          const stringVal = String(val).trim();
          return stringVal === "" || stringVal === "all"
            ? undefined
            : stringVal;
        };

        const normalizedValue = normalizeValue(value);

        switch (column.id) {
          case "status":
            setSearchParams({
              ...searchParams,
              status: normalizedValue,
              next: 1, // Reset to first page when filtering changes
            });
            break;
          case "categories":
            setSearchParams({
              ...searchParams,
              category: normalizedValue,
              next: 1,
            });
            break;
          case "provider":
            setSearchParams({
              ...searchParams,
              providerId: normalizedValue
                ? parseInt(normalizedValue, 10)
                : undefined,
              next: 1,
            });
            break;
          default:
            // For other columns, we don't support individual filtering
            // All filtering goes through the global search
            break;
        }
      },
    }));
  }, [columns, searchParams, setSearchParams]);

  // Convert data to table rows format (no processing, just formatting)
  const processedRows = useMemo(() => {
    return data.map((item, index) => ({
      id: String(index),
      original: item,
      index,
      getValue: (columnId: string) => {
        const column = columns.find((c) => c.id === columnId);
        return column?.accessorKey
          ? (item as Record<string, unknown>)[column.accessorKey as string]
          : undefined;
      },
    }));
  }, [data, columns]);

  // Global search methods
  const setGlobalSearch = (value: string) => {
    setSearchParams({
      ...searchParams,
      search: value && value.trim() ? value.trim() : undefined,
      next: 1, // Reset to first page when searching
    });
  };

  // Utility methods
  const resetFilters = () => {
    const {
      search: _search,
      status: _status,
      category: _category,
      providerId: _providerId,
      ...restParams
    } = searchParams;
    setSearchParams({
      ...restParams,
      next: 1,
    });
  };

  const resetSorting = () => {
    const {
      sortBy: _sortBy,
      sortOrder: _sortOrder,
      ...restParams
    } = searchParams;
    setSearchParams({
      ...restParams,
      next: 1,
    });
  };

  const reset = () => {
    const {
      search: _search,
      status: _status,
      category: _category,
      providerId: _providerId,
      sortBy: _sortBy,
      sortOrder: _sortOrder,
      ...restParams
    } = searchParams;
    setSearchParams({
      ...restParams,
      next: 1,
    });
  };

  // API compatible with the old useNativeTable hook
  return {
    // Data
    rows: processedRows,
    columns: columnInstances,

    // Global search (maps to API search parameter)
    globalFilter: searchParams.search || "",
    setGlobalFilter: setGlobalSearch,

    // Loading state
    isLoading,

    // Utilities
    getColumn: (columnId: string) =>
      columnInstances.find((c) => c.id === columnId),
    resetFilters,
    resetSorting,
    reset,

    // Current query state (for debugging/display)
    searchParams,
  };
}
