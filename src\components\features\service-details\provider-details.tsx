import { useTranslations } from "next-intl";

interface ProviderDetailsProps {
  provider: Provider;
  name: string;
}

// Helper functions to reduce complexity
const getProviderInfoItems = (provider: Provider, name: string): string[] => {
  return [
    `ID : #${provider.id}`,
    name || "Unknown User",
    provider.email ?? "No email provided",
    provider.unsafe_metadata?.username ?? "No username",
  ].filter(Boolean);
};

const getPopularityItems = (
  provider: Provider,
  t: ReturnType<typeof useTranslations>,
): string[] => {
  const country = provider.unsafe_metadata?.postal_address?.country ?? "France";
  const city = provider.unsafe_metadata?.postal_address?.city ?? "Paris";

  return [`${t("country")} : ${country}`, `${t("city")} : ${city}`];
};

const renderItems = (items: string[]) => {
  return items.map((item, index) => (
    <div
      key={index}
      className="flex flex-row items-center gap-2.5 px-2.5 h-auto"
    >
      <span className="font-['Poppins'] font-normal text-xs leading-[2.08em] text-left text-foreground">
        {item}
      </span>
    </div>
  ));
};

export function ProviderDetails({ provider, name }: ProviderDetailsProps) {
  const t = useTranslations("ServiceDetails");

  const providerInfoItems = getProviderInfoItems(provider, name);
  const popularityItems = getPopularityItems(provider, t);

  return (
    <>
      {/* Provider Information Section */}
      <div className="flex flex-col gap-2.5 flex-1">
        {/* Header */}
        <div className="bg-secondary flex flex-row justify-between items-center gap-2.5 px-2.5 h-auto">
          <span className="font-['Poppins'] font-medium text-xs leading-[2.08em] text-left text-foreground">
            {t("providerInformation")}
          </span>
        </div>

        {/* Content Items */}
        {renderItems(providerInfoItems)}
      </div>

      {/* Service Popularity Section */}
      <div className="flex flex-col gap-2.5 flex-1">
        {/* Header */}
        <div className="bg-secondary flex flex-row justify-between items-center gap-2.5 px-2.5 h-auto">
          <span className="font-['Poppins'] font-medium text-xs leading-[2.08em] text-left text-foreground">
            {t("servicePopularity")}
          </span>
        </div>

        {/* Content Items */}
        {renderItems(popularityItems)}
      </div>
    </>
  );
}
