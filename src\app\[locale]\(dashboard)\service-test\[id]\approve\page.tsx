import {
  AsyncServiceTestApprove,
  ServiceTestApproveSkeleton,
} from "@/components/features/service-test";
import { SearchParams } from "nuqs";
import { Suspense } from "react";

interface ServiceTestApprovePageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

export default function ServiceTestApprovePage({
  params,
  searchParams,
}: ServiceTestApprovePageProps) {
  return (
    <div className="flex flex-col gap-2.5 py-2.5 px-5">
      <Suspense fallback={<ServiceTestApproveSkeleton />}>
        <AsyncServiceTestApprove params={params} searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
