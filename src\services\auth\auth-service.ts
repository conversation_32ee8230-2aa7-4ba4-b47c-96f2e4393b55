import { BaseApiService as BaseService } from "../base-service";
import { DEFAULT_AUTH_CONFIG } from "@/config/env";
import {
  handleApiError,
  handleNetworkError,
  logAuthError,
} from "@/lib/auth-error-handler";

// All types are now available globally from src/types/auth/index.d.ts
// No need for local type definitions

/**
 * Clean Authentication Service
 * Focused solely on API communication following Single Responsibility Principle
 * No cookie storage logic - that's handled by server actions
 * Pure functions for authentication operations
 */
export class AuthService extends BaseService {
  private static instance: AuthService;
  private readonly apiUrl: string;
  private readonly endpoints = DEFAULT_AUTH_CONFIG.endpoints;

  constructor() {
    super();
    this.apiUrl = DEFAULT_AUTH_CONFIG.apiUrl;
  }

  // Singleton pattern to ensure single instance
  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Authenticate user with email and password
   * Pure API communication - no side effects like cookie storage
   * Returns raw authentication response for server actions to handle
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse | null> {
    try {
      const loginData: AdminLoginRequest = {
        email: credentials.email,
        password: credentials.password,
      };

      const response = await fetch(`${this.apiUrl}${this.endpoints.login}`, {
        method: "POST",
        headers: DEFAULT_AUTH_CONFIG.headers,
        body: JSON.stringify(loginData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const authError = handleApiError(response, errorData);
        logAuthError(authError, "AuthService.login");
        return null;
      }

      const authResponse: AuthResponse = await response.json();

      // Validate response structure
      if (!this.isValidAuthResponse(authResponse)) {
        logAuthError(
          handleNetworkError(new Error("Invalid auth response structure")),
          "AuthService.login",
        );
        return null;
      }

      // Return raw response - no cookie storage here
      return authResponse;
    } catch (error) {
      const authError = handleNetworkError(error);
      logAuthError(authError, "AuthService.login");
      return null;
    }
  }

  /**
   * Logout current user
   * Pure function - no side effects like cookie clearing
   * Server actions handle cookie management
   */
  async logout(): Promise<LogoutResponse> {
    try {
      // In a real implementation, this might call a logout endpoint
      // For now, just return success - server actions handle cookie clearing
      return {
        success: true,
        message: "Logout successful",
      };
    } catch (_error) {
      return {
        success: false,
        message: "An error occurred during logout",
      };
    }
  }

  /**
   * Refresh authentication token
   * Pure API communication for token refresh
   */
  async refreshToken(refreshToken: string): Promise<AuthResponse | null> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.refresh}`, {
        method: "POST",
        headers: DEFAULT_AUTH_CONFIG.headers,
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        console.error(
          "Token refresh failed:",
          response.status,
          response.statusText,
        );
        return null;
      }

      const authResponse: AuthResponse = await response.json();

      // Validate response structure
      if (!this.isValidAuthResponse(authResponse)) {
        console.error("Invalid refresh response structure");
        return null;
      }

      return authResponse;
    } catch (error) {
      console.error("Token refresh error:", error);
      return null;
    }
  }

  /**
   * Validate authentication token
   */
  async validateToken(token: string): Promise<ValidateTokenResponse> {
    try {
      const response = await fetch(`${this.apiUrl}${this.endpoints.profile}`, {
        method: "GET",
        headers: {
          ...DEFAULT_AUTH_CONFIG.headers,
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return {
          valid: false,
          error: {
            code: "INVALID_TOKEN",
            message: "Token is invalid or expired",
          },
        };
      }

      const admin: Admin = await response.json();

      return {
        valid: true,
        admin,
      };
    } catch (error) {
      return {
        valid: false,
        error: {
          code: "TOKEN_VALIDATION_ERROR",
          message: "Error validating token",
          details:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : { error: String(error) },
        },
      };
    }
  }

  /**
   * Validate AuthResponse structure
   */
  private isValidAuthResponse(data: unknown): data is AuthResponse {
    if (!data || typeof data !== "object") return false;

    const obj = data as Record<string, unknown>;

    return (
      typeof obj.accessToken === "string" &&
      typeof obj.refreshToken === "string" &&
      !!obj.admin &&
      typeof obj.admin === "object" &&
      obj.admin !== null &&
      typeof (obj.admin as Record<string, unknown>).id === "number" &&
      typeof (obj.admin as Record<string, unknown>).email === "string" &&
      typeof obj.expiresAt === "string"
    );
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
