import {
  AsyncServiceTest,
  ServiceTestSkeleton,
} from "@/components/features/service-test";
import { SearchParams } from "nuqs";
import { Suspense } from "react";

interface ServiceTestPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<SearchParams>;
}

export default function ServiceTestPage({
  params,
  searchParams,
}: ServiceTestPageProps) {
  return (
    <div className="flex flex-col gap-2.5 py-2.5 px-5">
      <Suspense fallback={<ServiceTestSkeleton />}>
        <AsyncServiceTest params={params} searchParams={searchParams} />
      </Suspense>
    </div>
  );
}
