/**
 * Authentication Error Handler
 * Centralized error handling for authentication operations
 * Following clean code principles for error management
 */

/**
 * Authentication error types
 * Provides clear categorization of different error scenarios
 */
export enum AuthErrorType {
  NETWORK_ERROR = "NETWORK_ERROR",
  INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
  TOKEN_EXPIRED = "TOKEN_EXPIRED",
  TOKEN_INVALID = "TOKEN_INVALID",
  SERVER_ERROR = "SERVER_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  NOT_FOUND = "NOT_FOUND",
  RATE_LIMITED = "RATE_LIMITED",
  UNKNOWN_ERROR = "UNKNOWN_ERROR",
}

/**
 * Structured authentication error
 * Provides consistent error structure across the application
 */
export interface AuthError {
  type: AuthErrorType;
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
}

/**
 * Error factory functions
 * Pure functions for creating specific error types
 */
export const createAuthError = {
  networkError: (message: string, details?: unknown): AuthError => ({
    type: AuthErrorType.NETWORK_ERROR,
    code: "NETWORK_ERROR",
    message,
    details,
    timestamp: new Date().toISOString(),
  }),

  invalidCredentials: (message: string = "Invalid credentials"): AuthError => ({
    type: AuthErrorType.INVALID_CREDENTIALS,
    code: "INVALID_CREDENTIALS",
    message,
    timestamp: new Date().toISOString(),
  }),

  tokenExpired: (message: string = "Token has expired"): AuthError => ({
    type: AuthErrorType.TOKEN_EXPIRED,
    code: "TOKEN_EXPIRED",
    message,
    timestamp: new Date().toISOString(),
  }),

  tokenInvalid: (message: string = "Token is invalid"): AuthError => ({
    type: AuthErrorType.TOKEN_INVALID,
    code: "TOKEN_INVALID",
    message,
    timestamp: new Date().toISOString(),
  }),

  serverError: (message: string, details?: unknown): AuthError => ({
    type: AuthErrorType.SERVER_ERROR,
    code: "SERVER_ERROR",
    message,
    details,
    timestamp: new Date().toISOString(),
  }),

  validationError: (message: string, details?: unknown): AuthError => ({
    type: AuthErrorType.VALIDATION_ERROR,
    code: "VALIDATION_ERROR",
    message,
    details,
    timestamp: new Date().toISOString(),
  }),

  unauthorized: (message: string = "Unauthorized access"): AuthError => ({
    type: AuthErrorType.UNAUTHORIZED,
    code: "UNAUTHORIZED",
    message,
    timestamp: new Date().toISOString(),
  }),

  forbidden: (message: string = "Access forbidden"): AuthError => ({
    type: AuthErrorType.FORBIDDEN,
    code: "FORBIDDEN",
    message,
    timestamp: new Date().toISOString(),
  }),

  notFound: (message: string = "Resource not found"): AuthError => ({
    type: AuthErrorType.NOT_FOUND,
    code: "NOT_FOUND",
    message,
    timestamp: new Date().toISOString(),
  }),

  rateLimited: (message: string = "Rate limit exceeded"): AuthError => ({
    type: AuthErrorType.RATE_LIMITED,
    code: "RATE_LIMITED",
    message,
    timestamp: new Date().toISOString(),
  }),

  unknown: (message: string, details?: unknown): AuthError => ({
    type: AuthErrorType.UNKNOWN_ERROR,
    code: "UNKNOWN_ERROR",
    message,
    details,
    timestamp: new Date().toISOString(),
  }),
};

/**
 * HTTP status code to error type mapping
 * Maps HTTP status codes to appropriate error types
 */
export function mapHttpStatusToErrorType(status: number): AuthErrorType {
  switch (status) {
    case 400:
      return AuthErrorType.VALIDATION_ERROR;
    case 401:
      return AuthErrorType.UNAUTHORIZED;
    case 403:
      return AuthErrorType.FORBIDDEN;
    case 404:
      return AuthErrorType.NOT_FOUND;
    case 429:
      return AuthErrorType.RATE_LIMITED;
    case 500:
    case 502:
    case 503:
    case 504:
      return AuthErrorType.SERVER_ERROR;
    default:
      return AuthErrorType.UNKNOWN_ERROR;
  }
}

/**
 * Error handler for API responses
 * Converts API errors to structured AuthError objects
 */
export function handleApiError(
  response: Response,
  errorData?: unknown,
): AuthError {
  const errorType = mapHttpStatusToErrorType(response.status);
  const message = getErrorMessage(errorData) || response.statusText;

  switch (errorType) {
    case AuthErrorType.VALIDATION_ERROR:
      return createAuthError.validationError(message, errorData);
    case AuthErrorType.UNAUTHORIZED:
      return createAuthError.unauthorized(message);
    case AuthErrorType.FORBIDDEN:
      return createAuthError.forbidden(message);
    case AuthErrorType.NOT_FOUND:
      return createAuthError.notFound(message);
    case AuthErrorType.RATE_LIMITED:
      return createAuthError.rateLimited(message);
    case AuthErrorType.SERVER_ERROR:
      return createAuthError.serverError(message, errorData);
    default:
      return createAuthError.unknown(message, errorData);
  }
}

/**
 * Error handler for network/fetch errors
 * Handles network-level errors and exceptions
 */
export function handleNetworkError(error: unknown): AuthError {
  if (error instanceof TypeError && error.message.includes("fetch")) {
    return createAuthError.networkError(
      "Network connection failed. Please check your internet connection.",
      error,
    );
  }

  if (error instanceof Error) {
    return createAuthError.unknown(error.message, {
      name: error.name,
      stack: error.stack,
    });
  }

  return createAuthError.unknown("An unexpected error occurred", error);
}

/**
 * Extract error message from various error data formats
 * Handles different API error response structures
 */
function getErrorMessage(errorData: unknown): string | null {
  if (!errorData) return null;

  if (typeof errorData === "string") return errorData;

  if (typeof errorData === "object" && errorData !== null) {
    const obj = errorData as Record<string, unknown>;

    // Try common error message fields
    if (typeof obj.message === "string") return obj.message;
    if (typeof obj.error === "string") return obj.error;
    if (typeof obj.detail === "string") return obj.detail;
    if (typeof obj.description === "string") return obj.description;
  }

  return null;
}

/**
 * Log authentication errors
 * Centralized logging for authentication errors
 */
export function logAuthError(error: AuthError, context?: string): void {
  const logData = {
    ...error,
    context,
    userAgent:
      typeof navigator !== "undefined" ? navigator.userAgent : "server",
  };

  // In development, log to console
  if (process.env.NODE_ENV === "development") {
    console.error("Auth Error:", logData);
  }

  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
  if (process.env.NODE_ENV === "production") {
    // TODO: Implement production error logging
    // errorTrackingService.captureError(logData);
  }
}

/**
 * Convert AuthError to user-friendly message
 * Provides user-friendly error messages for different error types
 */
/* eslint-disable */
export function getErrorDisplayMessage(error: AuthError): string {
  switch (error.type) {
    case AuthErrorType.NETWORK_ERROR:
      return "Connection failed. Please check your internet connection and try again.";
    case AuthErrorType.INVALID_CREDENTIALS:
      return "Invalid email or password. Please try again.";
    case AuthErrorType.TOKEN_EXPIRED:
      return "Your session has expired. Please log in again.";
    case AuthErrorType.TOKEN_INVALID:
      return "Authentication failed. Please log in again.";
    case AuthErrorType.UNAUTHORIZED:
      return "You are not authorized to access this resource.";
    case AuthErrorType.FORBIDDEN:
      return "Access denied. You don't have permission to perform this action.";
    case AuthErrorType.NOT_FOUND:
      return "The requested resource was not found.";
    case AuthErrorType.RATE_LIMITED:
      return "Too many requests. Please wait a moment and try again.";
    case AuthErrorType.SERVER_ERROR:
      return "Server error occurred. Please try again later.";
    case AuthErrorType.VALIDATION_ERROR:
      return error.message || "Please check your input and try again.";
    default:
      return "An unexpected error occurred. Please try again.";
  }
}
