import {
  ServicesListResponseSchema,
  ServiceDetailResponseSchema,
} from "@/schemas/services";
import { BaseApiService } from "../base-service";
import { GlobalQueryParamsType } from "@/types/global";

export class ServicesServices extends BaseApiService {
  private readonly BASE_URL = "admin/services";

  constructor() {
    super();
    this.list = this.list.bind(this);
    this.get = this.get.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.delete = this.delete.bind(this);
    this.getByUserId = this.getByUserId.bind(this);
    this.validate = this.validate.bind(this);
    this.reject = this.reject.bind(this);
  }

  /**
   * List all services with optional parameters
   * @param query - Global query parameters for filtering services
   * @returns Promise<ApiResponse<ServicesListResponse>>
   */
  async list(
    query: GlobalQueryParamsType,
  ): Promise<ApiResponse<ServicesListResponse>> {
    const {
      limit,
      next,
      currency: _currency,
      extl_id: _extl_id,
      display: _display,
      ...apiParams
    } = query;

    // Map frontend parameters to API parameters
    const apiQuery = {
      page: next, // Map 'next' to 'page' for API
      limit,
      ...apiParams, // Include search, status, category, providerId, sortBy, sortOrder
    };

    return await this.request<ServicesListResponse>(
      this.BASE_URL,
      {
        method: "GET",
        authenticated: true,
        tag: `services-list-${limit}-${next}`,
        query: apiQuery,
      },
      ServicesListResponseSchema,
    );
  }

  /**
   * Get a specific service by ID with optional query parameters
   * @param id - Service ID
   * @param query - Optional global query parameters
   * @returns Promise<ApiResponse<ServiceDetailResponse>>
   */
  async get(
    id: string,
    query?: GlobalQueryParamsType,
  ): Promise<ApiResponse<ServiceDetailResponse>> {
    return await this.request<ServiceDetailResponse>(
      `${this.BASE_URL}/${id}`,
      {
        method: "GET",
        authenticated: true,
        tag: `service-detail-${id}`,
        query,
      },
      ServiceDetailResponseSchema,
    );
  }

  /**
   * Get services by user ID
   * @param params - User services parameters
   * @returns Promise<ApiResponse<UserServicesResponse>>
   */
  async getByUserId(
    _params: UserServicesParams,
  ): Promise<ApiResponse<UserServicesResponse>> {
    // TODO: Implement user services fetching
    throw new Error("Method not implemented yet");
  }

  /**
   * Create a new service
   * @param data - Service creation data
   * @returns Promise<ApiResponse<Service>>
   */
  async create(_data: CreateServiceRequest): Promise<ApiResponse<Service>> {
    // TODO: Implement service creation
    throw new Error("Method not implemented yet");
  }

  /**
   * Update an existing service
   * @param data - Service update data
   * @returns Promise<ApiResponse<Service>>
   */
  async update(_data: EditServiceRequest): Promise<ApiResponse<Service>> {
    // TODO: Implement service update
    throw new Error("Method not implemented yet");
  }

  /**
   * Delete a service
   * @param params - Delete service parameters
   * @returns Promise<ApiResponse<void>>
   */
  async delete(params: DeleteServiceParams): Promise<ApiResponse<void>> {
    return await this.request<void>(`${this.BASE_URL}/${params.id}`, {
      method: "DELETE",
      authenticated: true,
      tag: `service-delete-${params.id}`,
    });
  }

  /**
   * Approve a service
   * @param serviceId - Service ID to approve
   * @returns Promise<ApiResponse<Service>>
   */
  async approve(serviceId: number): Promise<ApiResponse<Service>> {
    return await this.request<Service>(
      `${this.BASE_URL}/${serviceId}/approve`,
      {
        method: "POST",
        authenticated: true,
        tag: `service-approve-${serviceId}`,
        body: { adminComment: "Service approved" },
      },
    );
  }

  /**
   * Validate/Approve a service (legacy method for backward compatibility)
   * @param params - Validation parameters
   * @returns Promise<ApiResponse<Service>>
   */
  async validate(params: ValidateServiceParams): Promise<ApiResponse<Service>> {
    if (params.isPublished) {
      return this.approve(params.serviceId);
    } else {
      throw new Error("Use reject method for rejecting services");
    }
  }

  /**
   * Reject a service with a reason
   * @param serviceId - Service ID to reject
   * @param reason - Rejection reason
   * @returns Promise<ApiResponse<Service>>
   */
  async reject(
    serviceId: number,
    reason: string,
  ): Promise<ApiResponse<Service>> {
    return await this.request<Service>(`${this.BASE_URL}/${serviceId}/reject`, {
      method: "POST",
      authenticated: true,
      tag: `service-reject-${serviceId}`,
      body: { adminComment: reason },
    });
  }
}
