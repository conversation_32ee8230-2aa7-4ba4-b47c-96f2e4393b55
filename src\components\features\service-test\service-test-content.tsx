import { ServiceTestHeader } from "./service-test-header";
import { ServiceTestProviderInformation } from "./service-test-provider-information";
import { ServiceTestInformation } from "./service-test-information";

interface ServiceTestContentProps {
  service: Service;
  onBack: () => void;
}

/**
 * Main content component for service test details
 * Organizes the layout with improved design and structure
 * Follows clean code principles with single responsibility
 */
export function ServiceTestContent({
  service,
  onBack,
}: ServiceTestContentProps) {
  return (
    <div className="flex flex-col items-center gap-2.5 px-5 pb-2.5">
      <ServiceTestHeader onBack={onBack} />
      <ServiceTestProviderInformation 
        name={service.name} 
        provider={service.provider} 
      />
      <ServiceTestInformation service={service} />
    </div>
  );
}
