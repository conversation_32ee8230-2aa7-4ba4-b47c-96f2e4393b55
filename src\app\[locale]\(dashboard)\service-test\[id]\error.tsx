"use client";

import { ServiceTestError } from "@/components/features/service-test";
import { useParams } from "next/navigation";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Error page for service test details
 * Handles errors that occur during service test data fetching or rendering
 * Follows Next.js App Router error handling patterns
 */
export default function ServiceTestErrorPage({ error }: ErrorPageProps) {
  const params = useParams();
  const serviceId = params?.id as string;

  return <ServiceTestError error={error} serviceId={serviceId} />;
}
