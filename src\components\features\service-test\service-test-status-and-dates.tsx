import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";

interface ServiceTestStatusAndDatesProps {
  service: Service;
}

/**
 * Status and dates component for service test
 * Displays service status with improved visual indicators
 * Follows clean code principles with clear status representation
 */
export function ServiceTestStatusAndDates({ service }: ServiceTestStatusAndDatesProps) {
  const t = useTranslations("ServiceDetails");

  const getStatusVariant = (status: string) => {
    switch (status?.toUpperCase()) {
      case "APPROVED":
      case "ACTIVE":
        return "default"; // Green
      case "REJECTED":
        return "destructive"; // Red
      case "PENDING":
        return "secondary"; // Gray
      default:
        return "outline";
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t("notAvailable");
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return t("invalidDate");
    }
  };

  return (
    <div className="flex justify-between items-center w-full p-4">
      <div className="flex items-center gap-3">
        <Badge variant={getStatusVariant(service.status)} className="text-sm font-medium">
          {service.status || t("unknownStatus")}
        </Badge>
        {service.rejectionReason && (
          <span className="text-xs text-destructive">
            {t("rejectionReason")}: {service.rejectionReason}
          </span>
        )}
      </div>
      
      <div className="text-xs text-muted-foreground">
        {t("lastUpdated")}: {formatDate(service.updatedAt)}
      </div>
    </div>
  );
}
