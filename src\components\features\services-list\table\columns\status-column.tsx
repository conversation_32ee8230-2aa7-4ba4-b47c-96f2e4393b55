import { TableStatusBadge } from "../table-status-badge";

export const statusColumn: NativeTableColumn<Service> = {
  id: "status",
  accessorKey: "status",
  header: "Status",
  enableSorting: true,
  enableFiltering: true,
  // Server-side filtering - no client-side filterFn needed
  cell: ({ row }) => {
    const service = row.original;
    // Use the actual status field from the API response
    return <TableStatusBadge status={service.status} />;
  },
  size: 120,
};
